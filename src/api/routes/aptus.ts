// Base API URL for APTUS services
const baseUrl = import.meta.env.VITE_APTUS;

// Tipos de processos APTUS
export const aptusProcessTypes = {
  AD_APTUS: "PC_APTUS",
  CC_APTUS: "CC_APTUS",
  DR_APTUS: "DR_APTUS",
  PD_APTUS: "PD_APTUS",
  PC_APTUS: "PC_APTUS",
  IE_APTUS: "IE_APTUS",
  AD_TK: "AD_TK",
  PC_TK: "PC_TK",
  PD_TK: "PD_TK",
};

export const aptusRoutes: any = {
  AD_APTUS: "Advance",
  CC_APTUS: "CorporateCard",
  DR_APTUS: "RouteExpense",
  PD_APTUS: "SmallExpenses",
  PC_APTUS: "Accountability",
  IE_APTUS: "EducationalIncentive",
  AD_TK: "Advance/Turkey",
  PC_TK: "Accountability/Turkey",
  PD_TK: "SmallExpenses/Turkey",
};

// Rotas genéricas que podem ser usadas para todos os processos
export const getPendingApprovals = (processType?: string) => {
  const routeAcronim = processType ? aptusRoutes[processType] : "";
  return `${baseUrl}/${routeAcronim}`;
};

export const approve = (processType: string, document: string) => {
  const routeAcronim = processType ? aptusRoutes[processType] : "";

  return `${baseUrl}/${routeAcronim}/${document}/1`;
};

export const reproval = (
  processType: string,
  document: string,
  comentary: string
) => {
  const routeAcronim = processType ? aptusRoutes[processType] : "";

  return `${baseUrl}/${routeAcronim}/${document}/2?comentary=${comentary}`;
};

export const getDetails = (processType?: string) => {
  return processType
    ? `${baseUrl}/${processType}/getDetails`
    : `${baseUrl}/getDetails`;
};
