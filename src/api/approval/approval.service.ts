import { AllProcessesTypes } from "@/components/AccordionTable";
import { IUser } from "@/interfaces";
import {
  AptusApprovalService,
  AribaApprovalService,
  AvipamApprovalService,
  CommercialApprovalService,
  HRApprovalService,
  IntranetApprovalService,
  OnelogApprovalService,
  OracleApprovalService,
  ProcurementApprovalService,
  SapApprovalService,
  ServiceNowApprovalService,
} from "./strategy";
import {
  BaseApprovalService,
  IApprovalResponse,
} from "./strategy/base.service";
import { DocuSignApprovalService } from "./strategy/docusign.service";

const strategy: Partial<Record<ApprovalOrigin, any>> = {
  SAP: SapApprovalService,
  ARIBA: AribaApprovalService,
  COMMERCIAL: CommercialApprovalService,
  APTUS: AptusApprovalService,
  SERVICENOW: ServiceNowApprovalService,
  ORACLE: OracleApprovalService,
  HR: HRApprovalService,
  PROCUREMENT: ProcurementApprovalService,
  AVIPAM: AvipamApprovalService,
  ONELOG: OnelogApprovalService,
  INTRANET: IntranetApprovalService,
  DOCUSIGN: DocuSignApprovalService,
} as const;

export interface IApprovalService {
  origin?: ApprovalOrigin;
  rowData?: AllProcessesTypes;
  status?: string | boolean | number;
  reason?: string;
  // permission?: IPermissionsApproval;
  process?: string;
  cache?: boolean;
}

export interface ApprovalResponse<T = any> {
  success: boolean;
  message?: string;
  data?: {
    header: T;
    detail?: T;
  };
}

export type ApprovalOrigin =
  | "SAP"
  | "ARIBA"
  | "AVIPAM"
  | "HR"
  | "ONELOG"
  | "INTRANET"
  | "PROCUREMENT"
  | "APTUS"
  | "DOCUSIGN"
  | "COMMERCIAL"
  | "SERVICENOW"
  | "ORACLE"
  | "SEODIGITAL";

export class ApprovalService<T> {
  private apiService: BaseApprovalService<T>;
  user: IUser;

  constructor(private document: IApprovalService, user: IUser) {
    this.document = document;
    this.user = user;

    this.apiService = {} as BaseApprovalService<T>;
  }

  async approveReproveDocument(): Promise<IApprovalResponse<T>> {
    if (!this.document.origin) {
      throw new Error("Document origin is undefined");
    }
    this.apiService = new strategy[this.document.origin](
      this.document,
      this.user
    );

    return this.apiService.approveReproveDocument();
  }

  public static getApprovalStatus(
    origin: ApprovalOrigin,
    isApproval: boolean,
    isNcProcess: boolean = false
  ): string {
    const statusMap: Record<
      ApprovalOrigin,
      { approve: string; reject: string; return?: string }
    > = {
      SAP: { approve: "A", reject: "R", return: "B" },
      ARIBA: { approve: "A", reject: "D" },
      AVIPAM: { approve: "A", reject: "R" },
      HR: { approve: "A", reject: "R" },
      ONELOG: { approve: "A", reject: "R" },
      INTRANET: { approve: "A", reject: "R" },
      PROCUREMENT: { approve: "1", reject: "0" },
      APTUS: { approve: "1", reject: "2" },
      DOCUSIGN: { approve: "A", reject: "R" },
      COMMERCIAL: { approve: "A", reject: "R" },
      SERVICENOW: { approve: "A", reject: "R" },
      ORACLE: { approve: "APPROVED", reject: "REJECTED" },
      SEODIGITAL: { approve: "A", reject: "R" },
    };

    if (!statusMap[origin]) {
      throw new Error(`Invalid origin: ${origin}`);
    }

    if (isNcProcess) {
      return statusMap[origin].return ?? statusMap[origin].reject;
    }

    return isApproval ? statusMap[origin].approve : statusMap[origin].reject;
  }

  async getDocuments(): Promise<{ header: T[]; detail: T[] }> {
    const origins = Object.keys(strategy) as (keyof typeof strategy)[];
    const results: IApprovalResponse<T>[] = [];

    for (const origin of origins) {
      const ServiceClass = strategy[origin];
      if (ServiceClass) {
        const serviceInstance = new ServiceClass(this.document, this.user);
        try {
          const response = await serviceInstance.getDocuments();
          if (Array.isArray(response)) {
            results.push(...response);
          }
        } catch (error) {
          // Optionally handle/log error per origin
        }
      }
    }

    // Merge all headers and details
    const allHeaders = results.flatMap((r) => r.data?.header ?? []);
    const allDetails = results.flatMap((r) => r.data?.detail ?? []);

    if (allHeaders.length > 0 || allDetails.length > 0) {
      return {
        header: allHeaders,
        detail: allDetails,
      };
    }

    return {
      header: [],
      detail: [],
    };
  }
}
