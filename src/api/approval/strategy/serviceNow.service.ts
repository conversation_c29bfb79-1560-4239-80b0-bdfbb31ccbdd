import { processesProps } from "@/constant";
import { IPermissionsApproval, IServiceNow, IUser } from "@/interfaces";
import { ServiceNowService } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class ServiceNowApprovalService extends BaseApprovalService<IServiceNow.ItemProps> {
  cache: boolean = false;
  private activeServiceNowPermissions: IPermissionsApproval[] = [];
  private readonly origin = "SERVICENOW";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeServiceNowPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IServiceNow.ItemProps>
  > {
    const { rowData, status, reason } = this.document;

    const params: IServiceNow.ApprovalReprovalParams = {
      registerId: rowData.Registro,
      status: status as "A" | "R",
      comment: reason,
    };

    const response = await ServiceNowService.approvalReprovalDocument(params);

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: `${
        params.status === "A" ? t("Success.in.approving") : t("Failure.success")
      } ${t("Document")} ${params.registerId}`,
      success: response.success,
    };
  }

  private static serviceNowApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IServiceNow.ItemProps>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<IServiceNow.ItemProps>[]> {
    const promises: Promise<IApprovalResponse<IServiceNow.ItemProps>>[] = [];

    for (const permission of this.activeServiceNowPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);

      if (!ServiceNowApprovalService.serviceNowApiPromiseCache[key]) {
        ServiceNowApprovalService.serviceNowApiPromiseCache[key] =
          ServiceNowService.fetchAndPreparePendingApprovals(this.cache);
        promises.push(ServiceNowApprovalService.serviceNowApiPromiseCache[key]);
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do ServiceNow:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do ServiceNow encontrado.",
      },
    ];
  }
}
