import { processesProps } from "@/constant";
import { IPermissionsApproval, IUser } from "@/interfaces";
import {
  IProcurement,
  IProcurementApprovalReprovalParams,
} from "@/interfaces/procurement";
import { ProcurementService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class ProcurementApprovalService extends BaseApprovalService<IProcurement> {
  private readonly PROCUREMENT_PROCESS = ["ME", "MX"];
  private readonly JR_PROCESS = ["JR_MAN", "JR_CONF", "JR_CIRC", "JR_DEP"];
  cache: boolean = false;
  private activeProcurementPermissions: IPermissionsApproval[] = [];
  private readonly origin = "PROCUREMENT";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeProcurementPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        (processesProps(permission.process)[0]?.origin === this.origin ||
          permission.process === "JR")
    );
  }

  async approveReproveDocument(): Promise<IApprovalResponse<IProcurement>> {
    const { rowData, status, reason, process } = this.document;

    const params: IProcurementApprovalReprovalParams = {
      documentNumber: String(
        rowData?.numeroSolicitacao ?? rowData?.id ?? rowData?.Documento
      ),
      requestType: rowData?.tipoSolicitacao ?? rowData.requestType,
      operation: status as 1 | 0,
      comment: reason,
      userInitials: rowData?.initials,
    };

    const response = await ProcurementService.approvalReprovalDocument(
      params,
      process || ""
    );

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response.message ?? "",
      success: response.success,
    };
  }

  private static procurementApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IProcurement>>
  > = {};

  private async fetchProcess(
    processValue: string
  ): Promise<IApprovalResponse<IProcurement>> {
    const key = this.getCacheKey(processValue, this.cache);

    if (!ProcurementApprovalService.procurementApiPromiseCache[key]) {
      ProcurementApprovalService.procurementApiPromiseCache[key] =
        ProcurementService.getPendingApprovals(processValue, this.cache);
      return ProcurementApprovalService.procurementApiPromiseCache[key];
    }

    return Promise.resolve({
      success: false,
      message: "",
      data: { header: [], detail: [] },
    });
  }

  async getDocuments(): Promise<IApprovalResponse<IProcurement>[]> {
    const promises: Promise<IApprovalResponse<IProcurement>>[] = [];

    // Processa permissões regulares
    for (const permission of this.activeProcurementPermissions) {
      if (this.PROCUREMENT_PROCESS.includes(permission.process)) {
        promises.push(this.fetchProcess(permission.process));
      }

      if (this.JR_PROCESS.includes(permission.process)) {
        for (const process of this.JR_PROCESS) {
          promises.push(this.fetchProcess(process));
        }
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Procurement:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do Procurement encontrado.",
      },
    ];
  }
}
