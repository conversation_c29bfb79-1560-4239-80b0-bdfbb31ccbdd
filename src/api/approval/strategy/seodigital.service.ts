import { processesProps } from "@/constant";
import { IPermissionsApproval, ISeoDigital, IUser } from "@/interfaces";
import { SeoDigitalService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class SeoDigitalApprovalService extends BaseApprovalService<ISeoDigital.ISeoDigitalBase> {
  cache: boolean = false;
  private activeSeoDigitalPermissions: IPermissionsApproval[] = [];
  private readonly origin = "SEODIGITAL";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeSeoDigitalPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  private static seodigitalApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<ISeoDigital.ISeoDigitalBase>>
  > = {};

  async getDocuments(): Promise<
    IApprovalResponse<ISeoDigital.ISeoDigitalBase>[]
  > {
    const promises: Promise<IApprovalResponse<ISeoDigital.ISeoDigitalBase>>[] =
      [];

    for (const permission of this.activeSeoDigitalPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);
      if (!SeoDigitalApprovalService.seodigitalApiPromiseCache[key]) {
        SeoDigitalApprovalService.seodigitalApiPromiseCache[key] =
          SeoDigitalService.getPendingApprovals(this.cache);
        promises.push(SeoDigitalApprovalService.seodigitalApiPromiseCache[key]);
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do SeoDigital:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do SeoDigital encontrado.",
      },
    ];
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<ISeoDigital.ISeoDigitalBase>
  > {
    const { rowData, status, reason, process } = this.document;

    const seoDigitalParams = {
      processCode: rowData.id,
      approvalManagerId: rowData.approvalManagerId,
      typeOfApproval: rowData.statusApproval === "S" ? 2 : 1,
      status: status as "A" | "R",
      reason,
    };

    const response = await SeoDigitalService.approvalReprovalDocument(
      seoDigitalParams
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }
}
