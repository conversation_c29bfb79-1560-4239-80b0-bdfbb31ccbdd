import { processesProps } from "@/constant";
import i18n from "@/hooks/translation";
import { IPermissionsApproval, ISap, IUser } from "@/interfaces";
import { SapService } from "@/services";
import { handleLanguageSAP } from "@/utils/ValidationLanguage";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class SapApprovalService extends BaseApprovalService<ISap.ItemProps> {
  cache: boolean = false;
  private activeSapPermissions: IPermissionsApproval[] = [];
  private readonly origin = "SAP";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeSapPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  async approveReproveDocument(): Promise<IApprovalResponse<ISap.ItemProps>> {
    const { rowData, status, reason, process } = this.document;

    const sapParams = {
      document: rowData.documento,
      item: rowData.item || "",
      status: status as "A" | "R" | "B",
      additional1: rowData.additional1 || "",
      additional2: rowData.additional2 || "",
      reason: reason || "",
      language: handleLanguageSAP(i18n.language),
      process: process || "",
      ecp: rowData.ecp ?? !!["H5", "AM"].includes(process || ""),
    };

    const response = await SapService.approvalReprovalDocument(sapParams);
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response.message || "",
      success: response?.success || false,
    };
  }

  private static sapApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<ISap.ItemProps>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<ISap.ItemProps>[]> {
    const promises: Promise<IApprovalResponse<ISap.ItemProps>>[] = [];

    for (const permission of this.activeSapPermissions) {
      const sapParams = {
        process: permission.process || "",
        extraParams: {
          language: handleLanguageSAP(i18n.language),
          ecp: ["H5", "AM"].includes(permission.process ?? "")
            ? "true"
            : "false",
          cacheActive: `${this.cache}`,
          cacheLiveTime: 15,
        },
      };

      const key = this.getCacheKey(permission.process, this.cache);

      if (!SapApprovalService.sapApiPromiseCache[key]) {
        SapApprovalService.sapApiPromiseCache[key] =
          SapService.fetchAndPreparePendingApprovals(sapParams);
        promises.push(SapApprovalService.sapApiPromiseCache[key]);
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do SAP:", error);
    }

    return [
      {
        data: {
          header: [],
          detail: [],
        },
        success: false,
        message: "Nenhum documento do SAP encontrado.",
      },
    ];
  }
}
