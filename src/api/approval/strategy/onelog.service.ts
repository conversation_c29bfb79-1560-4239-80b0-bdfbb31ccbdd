import { processesProps } from "@/constant";
import { IOnelog, IPermissionsApproval, IUser } from "@/interfaces";
import { handleApiError, OneLogService } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class OnelogApprovalService extends BaseApprovalService<IOnelog.ClientProps> {
  private activeOnelogPermissions: IPermissionsApproval[] = [];
  cache: boolean = false;
  private readonly origin = "ONELOG";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;

    this.activeOnelogPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  private static onelogApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IOnelog.ClientProps>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<IOnelog.ClientProps>[]> {
    const promises: Promise<IApprovalResponse<IOnelog.ClientProps>>[] = [];
    for (const permission of this.activeOnelogPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);

      if (!OnelogApprovalService.onelogApiPromiseCache[key]) {
        OnelogApprovalService.onelogApiPromiseCache[key] =
          OneLogService.fetchAndPreparePendingApprovals(this.cache);
        promises.push(OnelogApprovalService.onelogApiPromiseCache[key]);
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Onelog:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do Onelog encontrado.",
      },
    ];
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IOnelog.ClientProps>
  > {
    const { rowData, status, reason, process } = this.document;

    try {
      const params = {
        status: status as "A" | "R",
        id: rowData.id,
        reason,
      };

      const response = await OneLogService.approvalReprovalDocument(params);

      return {
        success: response.success,
        message: response.message || "",
        data: {
          header: response?.data?.header || [],
          detail: response?.data?.detail || [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "ONELOG");
      return {
        success: false,
        message: "Error approving/reproving OneLog document",
        data: {
          header: [],
          detail: [],
        },
      };
    }
  }
}
