import { processesProps } from "@/constant";
import { IIntranet, IPermissionsApproval, IUser } from "@/interfaces";
import { IntranetService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class IntranetApprovalService extends BaseApprovalService<IIntranet.ItemProps> {
  private readonly INTRANET_PROCESS: string[] = [
    "C1",
    "C2",
    "CP",
    "CS",
    "NO",
    "NR",
    "PM",
    "TR",
  ];
  private readonly SV_PROCESSES: string[] = ["SV_1", "SV_2", "SV_3"];
  cache: boolean = false;
  private activeIntranetPermissions: IPermissionsApproval[] = [];
  private readonly origin = "INTRANET";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeIntranetPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        (processesProps(permission.process)[0]?.origin === this.origin ||
          ["SV", "TR"].includes(permission.process))
    );
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IIntranet.ItemProps>
  > {
    const { rowData, status, reason, process } = this.document;

    const params = {
      docId: rowData.CODIGO,
      status: status as "A" | "R",
      comments: reason,
    };

    const response = await IntranetService.approvalReprovalDocument(
      params,
      process as IIntranet.ProcessKey
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  private static intranetApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IIntranet.ItemProps>>
  > = {};

  private async fetchProcess(
    processValue: string
  ): Promise<IApprovalResponse<IIntranet.ItemProps>> {
    const key = this.getCacheKey(processValue, this.cache);

    if (!IntranetApprovalService.intranetApiPromiseCache[key]) {
      IntranetApprovalService.intranetApiPromiseCache[key] =
        IntranetService.fetchAndPreparePendingApprovals(
          processValue as IIntranet.ProcessKey,
          this.cache
        );
      return IntranetApprovalService.intranetApiPromiseCache[key];
    }

    return Promise.resolve({
      success: false,
      message: "",
      data: { header: [], detail: [] },
    });
  }

  async getDocuments(): Promise<IApprovalResponse<IIntranet.ItemProps>[]> {
    const promises: Promise<IApprovalResponse<IIntranet.ItemProps>>[] = [];

    for (const permission of this.activeIntranetPermissions) {
      if (this.INTRANET_PROCESS.includes(permission.process)) {
        promises.push(this.fetchProcess(permission.process));
      }

      if (this.SV_PROCESSES.includes(permission.process)) {
        this.SV_PROCESSES.forEach((process) => {
          promises.push(this.fetchProcess(process));
        });
      }
    }

    return [
      {
        data: {
          header: [],
          detail: [],
        },
        success: false,
        message: "Nenhum documento da Intranet encontrado.",
      },
    ];
  }
}
