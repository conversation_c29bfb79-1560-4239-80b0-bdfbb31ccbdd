import { processesProps } from "@/constant";
import { IDocusign, IPermissionsApproval, IUser } from "@/interfaces";
import { DocuSignService } from "@/services/docuSign";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class DocuSignApprovalService extends BaseApprovalService<IDocusign.ItemProps> {
  private activeDocusignPermissions: IPermissionsApproval[] = [];
  cache: boolean = false;
  private readonly origin = "DOCUSIGN";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.cache = this.document.cache ?? false;

    this.activeDocusignPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  // DocuSign não possui aprovação/reprovação, portanto retorna resposta padrão
  async approveReproveDocument(): Promise<
    IApprovalResponse<IDocusign.ItemProps>
  > {
    return {
      data: { header: [], detail: [] },
      success: false,
      message: "Aprovação/Reprovação não disponível para DocuSign.",
    };
  }

  private static docusignApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IDocusign.ItemProps>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<IDocusign.ItemProps>[]> {
    const promises: Promise<IApprovalResponse<IDocusign.ItemProps>>[] = [];

    for (const permission of this.activeDocusignPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);
      if (!DocuSignApprovalService.docusignApiPromiseCache[key]) {
        DocuSignApprovalService.docusignApiPromiseCache[key] =
          DocuSignService.getPendingApprovals(this.cache);
        promises.push(DocuSignApprovalService.docusignApiPromiseCache[key]);
      }
    }

    // Aguarda todas as promises e retorna os resultados individuais
    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((r) => r.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do DocuSign:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do DocuSign encontrado.",
      },
    ];
  }
}
