import { processesProps } from "@/constant";
import { <PERSON><PERSON><PERSON><PERSON>, IUser } from "@/interfaces";
import { AribaService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AribaApprovalService extends BaseApprovalService<
  IAriba.AribaItemType | IAriba.PendingApprovalsProps
> {
  private activeAribaPermissions: boolean = false;
  cache: boolean = false;
  private readonly origin = "ARIBA";

  private readonly ARIBA_PROCESSES = ["AP", "AS", "AR", "AT"];

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeAribaPermissions = this.permissions.some(
      (permission) =>
        this.ARIBA_PROCESSES.includes(permission.process) &&
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IAriba.AribaItemType | IAriba.PendingApprovalsProps>
  > {
    const { rowData, status, reason, process } = this.document;
    const aribaParams = {
      itemCode: rowData.task,
      action: status as string,
      comment: reason || "",
      aribaUser: rowData.aribaUser || "",
      typeProcess: process || "",
    };

    const response = await AribaService.approvalReprovalDocument(aribaParams);
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  private static aribaApiPromiseCache: Record<
    string,
    Promise<
      IApprovalResponse<IAriba.AribaItemType | IAriba.PendingApprovalsProps>
    >
  > = {};

  async getDocuments(): Promise<
    IApprovalResponse<IAriba.AribaItemType | IAriba.PendingApprovalsProps>[]
  > {
    const promises: Promise<
      IApprovalResponse<IAriba.AribaItemType | IAriba.PendingApprovalsProps>
    >[] = [];

    if (this.activeAribaPermissions) {
      // Verifica a troca de employeeid caso mude o usuário ou a mudança de cache
      const key = `${this.user.employeeID}-${this.cache}`;

      if (!AribaApprovalService.aribaApiPromiseCache[key]) {
        AribaApprovalService.aribaApiPromiseCache[key] =
          AribaService.fetchAndPreparePendingApprovals(this.cache);
        promises.push(AribaApprovalService.aribaApiPromiseCache[key]);
      }
    }

    // Aguarda todas as promises e retorna os resultados individuais
    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Ariba:", error);
    }

    return [
      {
        data: {
          header: [],
          detail: [],
        },
        success: false,
        message: "Nenhum documento do Ariba encontrado.",
      },
    ];
  }

  public static clearCacheForUser(userKey: string) {
    delete AribaApprovalService.aribaApiPromiseCache[userKey];
  }
}
