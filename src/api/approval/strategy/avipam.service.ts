import { processesProps } from "@/constant";
import { IAvipam, IPermissionsApproval, IUser } from "@/interfaces";
import { AvipamItem } from "@/interfaces/avipam";
import { AvipamService, handleApiError } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AvipamApprovalService extends BaseApprovalService<AvipamItem> {
  private activeAvipamPermissions: IPermissionsApproval[] = [];
  cache: boolean = false;
  private readonly origin = "AVIPAM";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = this.document.cache ?? false;

    this.activeAvipamPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  private static avipamApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<AvipamItem>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<AvipamItem>[]> {
    const promises: Promise<IApprovalResponse<AvipamItem>>[] = [];

    for (const permission of this.activeAvipamPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);
      if (!AvipamApprovalService.avipamApiPromiseCache[key]) {
        AvipamApprovalService.avipamApiPromiseCache[key] =
          AvipamService.fetchAndPreparePendingApprovals(this.cache);
        promises.push(AvipamApprovalService.avipamApiPromiseCache[key]);
      }
    }

    // Aguarda todas as promises e retorna os resultados individuais
    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((r) => r.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Avipam:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento da Avipam encontrado.",
      },
    ];
  }

  async approveReproveDocument(): Promise<IApprovalResponse<AvipamItem>> {
    try {
      const { rowData, status, reason, process } = this.document;

      const params: IAvipam.ApprovalReprovalParams = {
        orderNumber: rowData.orderNumber,
        status: status as "A" | "R",
        reason,
      };

      const response = await AvipamService.approvalReprovalDocument(params);

      return {
        success: response.success,
        message: response?.message || "",
        data: {
          header: response?.data?.header || [],
          detail: response?.data?.detail || [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "AVIPAM");
      return {
        data: {
          header: [],
          detail: [],
        },
        success: false,
        message: "Error approving/reproving AVIPAM document",
      };
    }
  }
}
