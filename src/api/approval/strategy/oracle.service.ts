import { oracleTypeRoute } from "@/api/routes/oracle";
import { I<PERSON><PERSON>le, IPermissionsApproval, IUser } from "@/interfaces";
import { OracleService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class OracleApprovalService extends BaseApprovalService<IOracle.OracleItemType> {
  private activeOraclePermissions: IPermissionsApproval[] = [];
  cache: boolean = false;

  private readonly TMS_PROCESSES = [
    "TMS_1",
    "TMS_2",
    "TMS_3",
    "TMS_4",
    "TMS_5",
    "TMS_6",
  ] as const;

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;

    this.activeOraclePermissions = this.permissions.filter(
      (permission) => permission.active && permission.process === "TMS"
    );
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IOracle.OracleItemType>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: IOracle.ApprovalReprovalParams = {
      id: rowData.id,
      statusCode: status as "APPROVED" | "REJECTED",
      obs: reason,
      email: rowData?.email || "",
      type: oracleTypeRoute[process || ""] as IOracle.OracleTypes,
    };

    const response = await OracleService.approvalReprovalDocument(params);

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response.success,
    };
  }

  private static oracleApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IOracle.OracleItemType>>
  > = {};

  async getDocuments(): Promise<IApprovalResponse<IOracle.OracleItemType>[]> {
    const promises: Promise<IApprovalResponse<IOracle.OracleItemType>>[] = [];

    if (this.activeOraclePermissions.length > 0) {
      for (const process of this.TMS_PROCESSES) {
        const key = this.getCacheKey(process, this.cache);

        if (!OracleApprovalService.oracleApiPromiseCache[key]) {
          OracleApprovalService.oracleApiPromiseCache[key] =
            OracleService.fetchAndPreparePendingApprovals(
              process as IOracle.OracleTypesProcess,
              this.cache
            );
          promises.push(OracleApprovalService.oracleApiPromiseCache[key]);
        }
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Oracle:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento da Oracle encontrado.",
      },
    ];
  }
}
