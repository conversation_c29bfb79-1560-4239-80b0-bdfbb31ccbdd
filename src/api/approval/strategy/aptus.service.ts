import {
  IAptus,
  IPermissionsApproval,
  IProcessApproval,
  IUser,
} from "@/interfaces";
import { AptusProcessType, AptusService } from "@/services/aptus";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AptusApprovalService extends BaseApprovalService<IAptus.IAptusBaseDocument> {
  processApproval: IProcessApproval[] = [];
  cache: boolean = false;
  private readonly PROCESS_MAP = {
    AD: "AD_APTUS",
    CC: "CC_APTUS",
    DR: "DR_APTUS",
    IE: "IE_APTUS",
    PC: "PC_APTUS",
    PD: "PD_APTUS",
  } as const;

  private readonly TURKEY_PROCESSES = ["AD_TK", "PC_TK", "PD_TK"] as const;

  private activeAptusPermissions: IPermissionsApproval[] = [];

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = document.cache ?? false;

    this.activeAptusPermissions = this.permissions.filter(
      (permission) => permission.active && permission.process === "AD"
    );
    this.processApproval = this.user.processApproval as IProcessApproval[];
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IAptus.IAptusBaseDocument>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: IAptus.ApprovalReprovalParams = {
      codigo: rowData.Codigo,
      isApprove: status === "1",
      processType: process as AptusProcessType,
      comentary: reason,
    };

    const response = await AptusService.approvalReprovalDocument(params);
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  private getProcessValue(processType: string): string {
    return (
      this.PROCESS_MAP[processType as keyof typeof this.PROCESS_MAP] ||
      processType
    );
  }

  private async fetchProcess(
    processValue: string
  ): Promise<IApprovalResponse<IAptus.IAptusBaseDocument>> {
    const key = this.getCacheKey(processValue, this.cache);

    if (!AptusApprovalService.aptusApiPromiseCache[key]) {
      AptusApprovalService.aptusApiPromiseCache[key] =
        AptusService.fetchAndPreparePendingApprovals(
          processValue as AptusProcessType,
          this.cache
        );
      return AptusApprovalService.aptusApiPromiseCache[key];
    }

    return Promise.resolve({
      success: false,
      message: "",
      data: { header: [], detail: [] },
    });
  }

  private static aptusApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IAptus.IAptusBaseDocument>>
  > = {};

  async getDocuments(): Promise<
    IApprovalResponse<IAptus.IAptusBaseDocument>[]
  > {
    const promises: Promise<IApprovalResponse<IAptus.IAptusBaseDocument>>[] =
      [];

    // Processa permissões regulares
    for (const permission of this.activeAptusPermissions) {
      // Processos regulares do Aptus
      const matchingProcesses = this.processApproval.filter(
        (process) => process.process === permission.process
      );

      for (const process of matchingProcesses) {
        const processValue = this.getProcessValue(process.typeProcess);
        promises.push(this.fetchProcess(processValue));
      }
    }

    // Processa permissões da Turquia
    for (const processValue of this.TURKEY_PROCESSES) {
      promises.push(this.fetchProcess(processValue));
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do Aptus:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do Aptus encontrado.",
      },
    ];
  }
}
