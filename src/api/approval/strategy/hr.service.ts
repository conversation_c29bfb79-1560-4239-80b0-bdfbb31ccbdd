import { processesProps } from "@/constant";
import { IHR, IPermissionsApproval, IUser } from "@/interfaces";
import { HRService } from "@/services/hr";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class HRApprovalService extends BaseApprovalService<IHR.HRDocument> {
  private activeHRPermissions: IPermissionsApproval[] = [];
  cache: boolean = false;
  private readonly SUFA_PROCESSES = ["SUFA_1", "SUFA_2", "SUFA_3", "SUFA_4"];
  private readonly origin = "HR";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.permissions = this.user.permissionsApproval as IPermissionsApproval[];
    this.cache = this.document.cache ?? false;

    this.activeHRPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        (processesProps(permission.process)[0]?.origin === this.origin ||
          permission.process === "SUFA")
    );
  }

  async approveReproveDocument(): Promise<IApprovalResponse<IHR.HRDocument>> {
    const { rowData, status, reason, process } = this.document;
    let response;

    if (process === "EPI") {
      const epiRow = rowData as IHR.IHRExceptionRequest;
      const params: IHR.EPIApprovalParams = {
        code: epiRow.CODIGO,
        requestType: epiRow.TIPOREQUISICAO,
        requestId: epiRow.IDREQUISICAO,
        epiId: epiRow.IDEPI,
        socManagerCode: epiRow.CODIGOGESTORSOC,
        rejectionReason: status !== "A" && status !== true ? reason : "",
        status: status as boolean,
      };

      response = await HRService.approvalReprovalDocumentEPI(params);
    } else {
      const documentId =
        rowData.Codigo || (rowData as IHR.IHRLearningManagement).documentNumber;

      response = await HRService.approvalReprovalDocument(
        documentId,
        process || "",
        status as "A" | "R" | boolean,
        reason
      );
    }

    return {
      data: response.data,
      message: "",
      success: response.success,
    };
  }

  private static hrApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<IHR.HRDocument>>
  > = {};

  private async fetchProcess(
    processValue: string
  ): Promise<IApprovalResponse<IHR.HRDocument>> {
    const key = this.getCacheKey(processValue, this.cache);

    if (!HRApprovalService.hrApiPromiseCache[key]) {
      HRApprovalService.hrApiPromiseCache[key] = HRService.getPendingApprovals(
        processValue,
        this.cache
      );
      return HRApprovalService.hrApiPromiseCache[key];
    }

    return Promise.resolve({
      success: false,
      message: "",
      data: { header: [], detail: [] },
    });
  }

  async getDocuments(): Promise<IApprovalResponse<IHR.HRDocument>[]> {
    const promises: Promise<IApprovalResponse<IHR.HRDocument>>[] = [];

    for (const permission of this.activeHRPermissions) {
      // Handle EPI and LM processes
      if (["EPI", "LM"].includes(permission.process)) {
        promises.push(this.fetchProcess(permission.process));
      }

      // Handle SUFA processes
      if (permission.process === "SUFA") {
        this.SUFA_PROCESSES.forEach((process) => {
          promises.push(this.fetchProcess(process));
        });
      }
    }

    try {
      const results = await Promise.all(promises);
      const successResults = results.filter((res) => res.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do HR:", error);
    }

    return [
      {
        data: { header: [], detail: [] },
        success: false,
        message: "Nenhum documento do HR encontrado.",
      },
    ];
  }
}
