import { IPermissionsApproval, IUser } from "@/interfaces";

export interface IApprovalResponse<T> {
  success: boolean;
  message: string;
  data: {
    header: T[];
    detail: T[];
  };
}

export abstract class BaseApprovalService<T> {
  protected user: IUser;
  protected permissions: IPermissionsApproval[] = [];

  protected constructor(user?: IUser) {
    this.user = user || ({} as IUser);
    this.permissions = this.user.permissionsApproval as IPermissionsApproval[];
  }

  protected getCacheKey(processValue: string, cache: boolean): string {
    return `${this.user?.employeeID}-${processValue}-${cache}`;
  }

  abstract approveReproveDocument(): Promise<IApprovalResponse<T>>;

  abstract getDocuments(): Promise<IApprovalResponse<T>[]>;
}
