import { processesProps } from "@/constant";
import { ICommercial, IPermissionsApproval, IUser } from "@/interfaces";
import { CommercialService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class CommercialApprovalService extends BaseApprovalService<ICommercial.ICommercialProps> {
  private activeCommercialPermissions: IPermissionsApproval[] = [];
  cache: boolean = false;
  private readonly origin = "COMMERCIAL";

  constructor(private document: IApprovalService, user: IUser) {
    super(user);
    this.document = document;
    this.cache = this.document.cache ?? false;

    this.activeCommercialPermissions = this.permissions.filter(
      (permission) =>
        permission.active &&
        processesProps(permission.process)[0]?.origin === this.origin
    );
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<ICommercial.ICommercialProps>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: ICommercial.IGCCParams | ICommercial.IPreCapParams =
      process === "GCC"
        ? {
            params: {
              comment: reason || "",
              status: status as string,
            },
            documentCode: rowData.id,
          }
        : {
            preCapNumber: rowData.preCapNumber,
            comment: reason || "",
            operation: status as string,
          };

    const response = await CommercialService.approvalReprovalDocument(
      process || "",
      params
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  private static commercialApiPromiseCache: Record<
    string,
    Promise<IApprovalResponse<ICommercial.ICommercialProps>>
  > = {};

  async getDocuments(): Promise<
    IApprovalResponse<ICommercial.ICommercialProps>[]
  > {
    const promise: Promise<IApprovalResponse<ICommercial.ICommercialProps>>[] =
      [];

    for (const permission of this.activeCommercialPermissions) {
      const key = this.getCacheKey(permission.process, this.cache);

      if (!CommercialApprovalService.commercialApiPromiseCache[key]) {
        CommercialApprovalService.commercialApiPromiseCache[key] =
          CommercialService.getPendingApprovals(permission.process, this.cache);
        promise.push(CommercialApprovalService.commercialApiPromiseCache[key]);
      }
    }

    // Aguarda todas as promises e retorna os resultados individuais
    try {
      const results = await Promise.all(promise);
      const successResults = results.filter((r) => r.success);
      if (successResults.length > 0) {
        return successResults;
      }
    } catch (error) {
      console.error("Erro ao buscar documentos do DocuSign:", error);
    }

    return [
      {
        data: {
          header: [],
          detail: [],
        },
        success: false,
        message: "Nenhum documento do Commercial encontrado.",
      },
    ];
  }
}
