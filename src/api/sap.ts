import { handleApiError } from "@/services";
import { t } from "i18next";
import { IResponse, ISap } from "../interfaces";
import { getLocalStorageItem } from "../utils/storage";
import { api, objectCatch } from "./api";
import { RouteSap } from "./routes";

export type ExtraParamsType = {
  language: string;
  cacheLiveTime: number;
  ecp: string;
  cacheActive: string;
  ano?: number;
  aprovador?: string;
  timeKeepCache?: string;
  [key: string]: any;
};

export const getPendingApprovals = async ({
  process,
  extraParams,
}: {
  process: string;
  extraParams: ExtraParamsType;
}): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.get(RouteSap.getPendingApprovals, {
      params: {
        ...extraParams,
        process,
      },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return {
      data,
      success: status === 200,
    };
  } catch (error) {
    handleApiError(error, t, "SAP", process);
    return { ...objectCatch, data: {} as ISap.ReturnProps };
  }
};

export const getDocumentDetails = async (
  documentNumber: string,
  process: string,
  additional1?: string
): Promise<IResponse.Default<ISap.ReturnProps>> => {
  try {
    const { data, status } = await api.get(
      RouteSap.getDocumentDetails(documentNumber, process, additional1),
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "SAP", process);
    return { ...objectCatch, data: {} as ISap.ReturnProps };
  }
};

export const approvalReprovalDocument = async (
  params: ISap.ApprovalReprovalParams
): Promise<IResponse.Default<ISap.ApprovalReprovalResponse>> => {
  try {
    const { data, status } = await api.put(
      RouteSap.approvalReprovalDocument(params.document),
      params
    );

    return { data, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "SAP", params.process);
    return {
      ...objectCatch,
      data: {} as ISap.ApprovalReprovalResponse,
    };
  }
};

export const getDpOptions = async (): Promise<
  IResponse.Default<ISap.DPOptions[]>
> => {
  try {
    const { data, status } = await api.get(
      `${import.meta.env.VITE_DOMAIN}/SapService/WeightDivergence/ListReason`,
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    // handleApiError(error, t, "SAP", process);
    return { ...objectCatch, data: {} as ISap.DPOptions[] };
  }
};
