import { getLocalStorageItem } from "@/utils/storage";
import { IPermissionsApproval, IResponse, ISearchUser } from "../interfaces";
import { api, arrayCatch } from "./api";
import { RouteSearchUsers } from "./routes";

export interface SearchUserResponse {
  accounts: ISearchUser.SearchUsersResponse[]; // Replace 'any' with the actual user account type if available
  total: number;
}

interface SubordinateUser {
  id: number;
  initials: string;
  name: string;
  employeeId: string;
  permissionsApproval?: IPermissionsApproval[];
}

export const searchUsers = async (
  value: string,
  page: number,
  action: string,
  userId: string
): Promise<IResponse.Default<SearchUserResponse>> => {
  try {
    const { data, status } = await api.get(
      action === "reports" || action === "admin"
        ? RouteSearchUsers.searchUsersRepresenting(value, page)
        : RouteSearchUsers.searchUsersSubordinates(value, userId, page),
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    // Tratamento específico para cada tipo de resposta
    let filteredOptions;
    let totalOptions;

    if (action === "reports" || action === "admin") {
      filteredOptions = data.accounts;
      totalOptions = data.qttyRegisters;
    } else {
      const subordinatesData: SubordinateUser[] = data || [];
      filteredOptions = subordinatesData;
      totalOptions = subordinatesData.length;
    }

    return {
      data: {
        accounts: filteredOptions,
        total: totalOptions,
      },
      success: status === 200,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      ...arrayCatch,
      data: {
        accounts: [] as ISearchUser.SearchUsersResponse[],
        total: 0,
      },
    };
  }
};
