import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Box, Typography } from "@mui/material";

import { useCards } from "@/stores/cardsStore";

import { AccordionTable } from "@/components";
import Loading from "@/components/Loading";
import { useGetDocuments } from "@/hooks/useGetDocuments";
import { IUser } from "@/interfaces";

export const ApprovalsPendenciesContent = ({ user }: { user: IUser }) => {
  const { t } = useTranslation();

  const { cards, handleCardResponse, reloadWithoutCache } = useCards(user);
  const { getDocuments, isLoadingDocuments } = useGetDocuments(user);

  useEffect(() => {
    if (user?.authenticated) {
      try {
        const params = {
          cache: !reloadWithoutCache, // se reloadWithoutCache for true, não usa cache
          handleCardResponse,
        };
        getDocuments(params);
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    }
  }, [reloadWithoutCache, user]);

  return (
    <>
      {isLoadingDocuments ? (
        <Loading open={isLoadingDocuments} />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          {cards.length === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {t("NoPendingItems")}
              </Typography>
            </Box>
          )}
          {cards.length > 0 && (
            <section>
              {cards?.map((card) => {
                return (
                  <AccordionTable
                    key={`${card.process}-${card.type}`}
                    process={card.process}
                    title={card.title}
                    headerData={card.headerData}
                    detailData={card.detailData}
                    total={card.total}
                    type={card.type}
                  />
                );
              })}
            </section>
          )}
        </Box>
      )}
    </>
  );
};
