import { ApprovalOrigin } from "@/api/approval/approval.service";
import { AribaApprovalService } from "@/api/approval/strategy";
import { AllProcessesTypes } from "@/components/AccordionTable";
import Loading from "@/components/Loading";
import { MassApprovalReprovalButton } from "@/components/MassApprovalReprovalButton";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useGetDocuments } from "@/hooks/useGetDocuments";
import { useCards } from "@/stores/cardsStore";
import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { Box, Card, CardContent, FormControl, TextField, Typography, InputAdornment } from "@mui/material";
import { Search } from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { t } from "i18next";
import { JSX, useEffect, useState } from "react";
import { AccordionTable } from "../../components";
import { useAuth } from "../../hooks";
import { useSearchApprovalsText } from "@/hooks/useSearchApprovalsText";

const sectionStyle = (isMobile: boolean) => ({
  padding: isMobile ? "2rem 1rem" : "2rem 0rem",
});

const EmptySection = ({ isMobile }: { isMobile: boolean }) => (
  <section style={sectionStyle(isMobile)}>
    <Card>
      <CardContent>
        <Typography component="span" sx={{ fontWeight: "bold" }}>
          {t("No.pending.items")}
        </Typography>
      </CardContent>
    </Card>
  </section>
);

const Home = (): JSX.Element => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { user } = useAuth();

  const { selectedRows, clearSelectedRows } = useSelectedRowsStore();

  const { approveReproveDocument, isLoadingApproval, getApprovalStatus } =
    useApprovalDocument();

  const { cards, handleCardResponse, reloadWithoutCache } = useCards(user);

  const { getDocuments, isLoadingDocuments } = useGetDocuments(user);

  const { searchInputToggled } = useSearchApprovalsText()

  useEffect(() => {
    if (user?.authenticated) {
      AribaApprovalService.clearCacheForUser(user.accessToken);
    }
  }, [
    user?.authenticated,
    user?.accessToken,
    user?.userReading,
    user?.permissionsApproval,
    user,
  ]);

  useEffect(() => {
    try {
      const params = {
        cache: !reloadWithoutCache, // se reloadWithoutCache for true, não usa cache
        handleCardResponse,
      };
      getDocuments(params);
    } catch (error) {
      console.error("Error fetching documents:", error);
    }
  }, [reloadWithoutCache]);

  const [reprovalModalState, setReprovalModalState] = useState<{
    open: boolean;
    rows: AllProcessesTypes[];
  }>({
    open: false,
    rows: [],
  });

  const handleOpenReprovalModal = (rows: AllProcessesTypes[]) => {
    setReprovalModalState({
      open: true,
      rows,
    });
  };

  const handleCloseReprovalModal = () => {
    setReprovalModalState({
      open: false,
      rows: [],
    });
  };

  const handleMassReproval = () => {
    if (!Object.keys(selectedRows)?.length) return;
    const flattenedRows = Object.values(selectedRows).flat();
    handleOpenReprovalModal(flattenedRows);
  };

  const handleConfirmMassReproval = async (reason: string) => {
    const rows = reprovalModalState.rows;
    if (!rows?.length) return;

    try {
      const promises = Object.values(selectedRows)
        .flat()
        .map(async (row) => {
          return approveReproveDocument({
            rowData: row,
            origin: row.origin as ApprovalOrigin,
            process: row.type,
            reason,
            status: getApprovalStatus(
              row.origin as ApprovalOrigin,
              false,
              row.origin === "SAP" && row.process === "NC"
            ),
          });
        });

      await Promise.all(promises);
    } catch (error) {
      console.error("Error during mass reproval:", error);
    }

    // Clear selection after processing
    clearSelectedRows();

    handleCloseReprovalModal();
  };

  const handleMassApproval = async () => {
    if (!Object.keys(selectedRows)?.length) return;

    try {
      const promises = Object.values(selectedRows)
        .flat()
        .map(async (row) => {
          return approveReproveDocument({
            rowData: row,
            origin: row.origin as ApprovalOrigin,
            process: row.type,
            status: getApprovalStatus(
              row.origin as ApprovalOrigin,
              true,
              false
            ),
          });
        });

      await Promise.all(promises);
    } catch (error) {
      console.error("Error during mass approval:", error);
    }

    clearSelectedRows();
  };

  return (
    <>
      {isLoadingDocuments || isLoadingApproval ? (
        <Loading open={isLoadingDocuments || isLoadingApproval} />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          {cards.length === 0 &&
            <EmptySection isMobile={isMobile} />
          }
          {cards.length > 0 && (
            <section style={sectionStyle(isMobile)}>
              {searchInputToggled && (
                <FormControl
                  sx={{
                    marginBottom: '1rem'
                  }}
                  fullWidth
                >
                  <TextField
                    fullWidth
                    placeholder={t("SearchOnCentral")}
                    variant="outlined"
                    sx={{
                      backgroundColor: "white",
                      borderRadius: 1,
                    }}
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search />
                          </InputAdornment>
                        ),
                      },
                    }}
                  />
                </FormControl>
              )}
              {cards?.map((card) => {
                return (
                  <AccordionTable
                    key={`${card.process}-${card.type}`}
                    process={card.process}
                    title={card.title}
                    headerData={card.headerData}
                    detailData={card.detailData}
                    total={card.total}
                    type={card.type}
                  />
                );
              })}
            </section>
          )}
        </Box>
      )}
      <MassApprovalReprovalButton
        handleMassReproval={handleMassReproval}
        handleConfirmMassReproval={handleConfirmMassReproval}
        handleMassApproval={handleMassApproval}
        reprovalModalState={reprovalModalState}
        handleCloseReprovalModal={handleCloseReprovalModal}
      />
    </>
  );
};

export default Home;
