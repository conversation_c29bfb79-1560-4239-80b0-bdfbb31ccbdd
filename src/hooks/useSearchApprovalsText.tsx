import React, { createContext, useContext, useState, ReactNode } from "react";

interface SearchApprovalsTextContextData {
  searchInputToggled: boolean;
  searchApprovalsTextInputValue: string;
  handleSearchApprovalsTextInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleToggleSearchInput: () => void;
}

const SearchApprovalsTextContext = createContext<SearchApprovalsTextContextData>(
  {} as SearchApprovalsTextContextData
);

interface SearchApprovalsTextProviderProps {
  children: ReactNode;
}

export const SearchApprovalsTextProvider: React.FC<SearchApprovalsTextProviderProps> = ({ children }) => {
  const [searchInputToggled, setSearchInputToggled] = useState(false);
  const [searchApprovalsTextInputValue, setSearchApprovalsTextInputValue] = useState("");

  const handleToggleSearchInput = () => {
    setSearchInputToggled((previousState) => !previousState);
  };

  const handleSearchApprovalsTextInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchApprovalsTextInputValue(event.target.value);
  };

  return (
    <SearchApprovalsTextContext.Provider
      value={{
        searchInputToggled,
        handleToggleSearchInput,

        searchApprovalsTextInputValue,
        handleSearchApprovalsTextInputChange,
      }
      }
    >
      {children}
    </SearchApprovalsTextContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useSearchApprovalsText = (): SearchApprovalsTextContextData => {
  const context = useContext(SearchApprovalsTextContext);

  if (!context) {
    throw new Error("useSearchApprovalsText must be used within a SearchApprovalsTextProvider");
  }

  return context;
};
