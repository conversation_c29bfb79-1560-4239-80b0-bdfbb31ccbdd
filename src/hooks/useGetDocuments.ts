import {
  ApprovalService,
  IApprovalService,
} from "@/api/approval/approval.service";
import { AllProcessesTypes } from "@/components/AccordionTable";
import { IUser } from "@/interfaces";
import { useMutation } from "react-query";

export function useGetDocuments(user: IUser) {
  const { mutate: getDocuments, isLoading: isLoadingDocuments } = useMutation(
    async ({
      cache = true,
      handleCardResponse,
    }: {
      cache?: boolean;
      handleCardResponse: <T>(header: T[], detail: T[]) => void;
    }) => {
      try {
        const document = {
          cache,
        };
        const service = new ApprovalService(document as IApprovalService, user);

        const response = await service.getDocuments();

        if (response.header.length > 0) {
          handleCardResponse<AllProcessesTypes>(
            response.header,
            response.detail
          );
        }
        return { response };
      } catch (error) {
        return { error };
      }
    }
  );

  return { getDocuments, isLoadingDocuments };
}
