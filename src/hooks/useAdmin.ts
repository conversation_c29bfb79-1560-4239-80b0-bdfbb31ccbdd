import { useState } from "react";

import type { ISearchUser } from "@/interfaces";

export interface UseAdminReturn {
  currentTab: string;
  selectedValue: ISearchUser.SearchUsersAccounts[];

  setSelectedValue: (value: ISearchUser.SearchUsersAccounts[]) => void;
  handleTabChange: (value: string) => void;
}

export const useAdmin = (): UseAdminReturn => {
  const [currentTab, setCurrentTab] = useState("1");
  const [selectedValue, setSelectedValue] = useState<
    ISearchUser.SearchUsersAccounts[]
  >([]);

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };

  return {
    currentTab,
    selectedValue,
    handleTabChange,
    setSelectedValue,
  };
};
