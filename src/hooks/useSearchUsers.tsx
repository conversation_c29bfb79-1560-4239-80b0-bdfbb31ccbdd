import { t } from "i18next";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { APIRepresentateAccount, APISearchUsers } from "../api";
import {
  IAccordionTableProps,
  IPermissionsApproval,
  ISearchUser,
  IUser,
} from "../interfaces";
import { GetTokenForThisUser, useAuth } from "./auth";

interface UseSearchUsersReturn {
  selectedValue: ISearchUser.SearchUsersAccounts[];
  inputValue: string;
  options: ISearchUser.SearchUsersAccounts[];
  isLoadingOptions: boolean;
  loadMore: boolean;
  handleChange: (
    value: ISearchUser.SearchUsersAccounts[],
    onClose?: () => void
  ) => void;
  handleInputChange: (input: string) => void;
  setLoadMore: (value: boolean) => void;
  loadMoreResults: () => void;
  resetValues: () => void;
  setInputValue: (value: string) => void;
  setSelectedValue: (value: ISearchUser.SearchUsersAccounts[]) => void;
}

export const useSearchUsers = (
  getTokenForThisUser: (params: GetTokenForThisUser) => Promise<string | null>,
  login: () => Promise<IUser>,
  setCards: (cards: IAccordionTableProps<any>[]) => void,
  action: "subordinate" | "reports" | "admin"
): UseSearchUsersReturn => {
  const { user } = useAuth();
  const [selectedValue, setSelectedValue] = useState<
    ISearchUser.SearchUsersAccounts[]
  >([]);
  const [inputValue, setInputValue] = useState<string>("");
  const [options, setOptions] = useState<ISearchUser.SearchUsersAccounts[]>([]);
  const [page, setPage] = useState(0);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);
  const [loadMore, setLoadMore] = useState(false);

  // Refs para controlar debounce e cancelamento de requisições
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastSearchValueRef = useRef<string>("");
  const requestIdRef = useRef<number>(0);

  const navigate = useNavigate();

  const handleSetRepresentativeUser = useCallback(async (id: number) => {
    try {
      const accountIdOriginal = localStorage.getItem("@BRFApprovalsInitial");
      if (accountIdOriginal) {
        localStorage.setItem("@BRFApprovalsRepresent", "true");
        const params = {
          accountIdRepresentate: id,
          accountIdOriginal: Number(accountIdOriginal),
        };
        try {
          const { success } = await APIRepresentateAccount.representateAccount(
            params
          );
          if (success) {
            setCards([]);

            login().then(() => {
              navigate("/");
            });
          }
        } catch (e) {}
      }
    } catch (err) {
      toast.error(t("Oops.Something.went.wrong.Try.again.later."));
    }
  }, []);

  const searchCardsOfSpecificUser = useCallback(
    async (
      name: string,
      employeeId: string,
      permissions: IPermissionsApproval[],
      action?: "subordinate" | "reports" | "admin"
    ) => {
      try {
        setCards([]);

        console.log(name, employeeId, permissions, action);

        await getTokenForThisUser({
          employeeId,
          action,
          permissionsApproval: permissions,
          userReading: name,
        });

        if (action === "subordinate" && location.pathname !== "/") {
          navigate(`/${location.search}`);
        }
      } catch (error) {
        console.error("Erro ao representar subordinado:", error);

        // Mostra uma mensagem mais específica baseada no tipo de erro
        if (error instanceof Error) {
          if (error.message.includes("Token not found")) {
            toast.error(
              "Token do subordinado não foi encontrado na resposta da API"
            );
          } else if (error.message.includes("Authentication error")) {
            toast.error("Erro de autenticação ao buscar dados do subordinado");
          } else {
            toast.error(`Erro ao representar subordinado: ${error.message}`);
          }
        } else {
          toast.error("Não foi possivel representar esse subordinado");
        }
      }
    },
    [getTokenForThisUser, navigate, setCards]
  );

  useEffect(() => {
    // Lógica a ser executada quando selectedValue mudar
    console.log(selectedValue, "selectedValue");
  }, [selectedValue]);

  const handleChange = useCallback(
    (value: ISearchUser.SearchUsersAccounts[], onClose?: () => void) => {
      console.log(value, "value do handleChange");
      console.log(action, "value do handleChange");
      debugger;
      if (!value?.length) return;

      setSelectedValue(value);

      // Garante que só uma ação será executada
      if (action === "reports") {
        setCards([]);
        handleSetRepresentativeUser(value[0].id);
      } else if (action === "admin" || action === "subordinate") {
        searchCardsOfSpecificUser(
          value[0].name,
          value[0].employeeId,
          value[0].permissionsApproval,
          action
        );
      }

      if (onClose) {
        onClose();
      }
    },
    [action]
  );

  // Função para cancelar requisições pendentes
  const cancelPendingRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Função para limpar debounce
  const clearDebounce = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
  }, []);

  // Função para fazer a busca na API
  const performSearch = useCallback(
    async (searchValue: string, userId: string, pageNumber: number = 0) => {
      // Cancela requisições anteriores
      cancelPendingRequests();

      // Cria um novo AbortController para esta requisição
      abortControllerRef.current = new AbortController();

      // Gera um ID único para esta requisição
      const currentRequestId = ++requestIdRef.current;

      try {
        setIsLoadingOptions(true);

        // Cria uma Promise que será rejeitada se a requisição for cancelada
        const abortPromise = new Promise((_, reject) => {
          abortControllerRef.current?.signal.addEventListener("abort", () => {
            reject(new Error("Request aborted"));
          });
        });

        // Faz a requisição à API
        const apiPromise = APISearchUsers.searchUsers(
          searchValue.trim(),
          pageNumber,
          action,
          userId
        );

        // Usa Promise.race para permitir cancelamento
        const result = (await Promise.race([apiPromise, abortPromise])) as any;
        const { data } = result;

        // // Verifica se esta ainda é a requisição mais recente
        // if (currentRequestId !== requestIdRef.current) {
        //   return;
        // }

        // // Verifica se a requisição foi cancelada
        // if (abortControllerRef.current?.signal.aborted) {
        //   return;
        // }

        // Atualiza o loadMore baseado na resposta
        if (data?.accounts?.length === data?.total) {
          setLoadMore(false);
        } else {
          setLoadMore(true);
        }

        // Só atualiza se ainda for a busca mais recente
        if (lastSearchValueRef.current === searchValue) {
          if (pageNumber > 0) {
            setOptions((prevOptions) => [...prevOptions, ...data.accounts]);
          } else {
            setOptions(data.accounts);
          }
          setPage(pageNumber + 1);
        } else {
        }
      } catch (error: any) {
        // Ignora erros de cancelamento
        if (error.message === "Request aborted") {
          return;
        }

        // // Só processa erro se for da requisição mais recente
        // if (currentRequestId === requestIdRef.current) {
        //   toast.error("Erro ao buscar usuários. Tente novamente.");
        // }
      } finally {
        // Só remove loading se for da requisição mais recente
        if (currentRequestId === requestIdRef.current) {
          setIsLoadingOptions(false);
        }
      }
    },
    [action, cancelPendingRequests]
  );

  const handleInputChange = useCallback(
    async (value: string) => {
      if (value?.length < 3) {
        // Se menos de 3 caracteres, limpa tudo
        clearDebounce();
        cancelPendingRequests();
        setOptions([]);
        setLoadMore(false);
        setIsLoadingOptions(false);
        setPage(0);
        lastSearchValueRef.current = "";
        return;
      }

      setInputValue(value);
      lastSearchValueRef.current = value;

      // Limpa o debounce anterior
      // clearDebounce();

      // Se o valor mudou, reseta a página
      if (inputValue !== value) {
        setPage(0);
      }

      // Imediatamente marca como loading quando vai iniciar busca
      setIsLoadingOptions(true);

      // Implementa debounce de 300ms
      debounceTimeoutRef.current = setTimeout(() => {
        const pageNumber = inputValue === value && page > 0 ? page : 0;
        performSearch(value, user?.employeeID, pageNumber);
      }, 300);
    },
    [
      inputValue,
      page,
      clearDebounce,
      performSearch,
      cancelPendingRequests,
      user?.employeeID,
    ]
  );

  // Função para carregar mais resultados
  const loadMoreResults = useCallback(() => {
    if (lastSearchValueRef.current && !isLoadingOptions) {
      performSearch(lastSearchValueRef.current, user?.employeeID || "", page);
    }
  }, [page, isLoadingOptions, performSearch, user?.employeeID]);

  // Cleanup quando o componente for desmontado
  // useEffect(() => {
  //   return () => {
  //     clearDebounce();
  //     cancelPendingRequests();
  //   };
  // }, [clearDebounce, cancelPendingRequests]);

  const resetValues = () => {
    // Limpa debounce e cancela requisições pendentes
    clearDebounce();
    cancelPendingRequests();

    // Reseta todos os estados
    setSelectedValue([]);
    setInputValue("");
    setPage(0);
    setOptions([]);
    setLoadMore(false);
    setIsLoadingOptions(false);

    // Limpa refs
    lastSearchValueRef.current = "";
    requestIdRef.current = 0;
  };

  return {
    selectedValue,
    inputValue,
    isLoadingOptions,
    options,
    handleChange,
    handleInputChange,
    loadMore,
    setLoadMore,
    loadMoreResults,
    resetValues,
    setInputValue,
    setSelectedValue,
  };
};
