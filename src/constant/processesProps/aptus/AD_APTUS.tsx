import { Notifications, Warning } from "@mui/icons-material";
import { t } from "i18next";
import { IAptus } from "../../../interfaces";
import { AptusService } from "../../../services/aptus";
import { dateBDtoRead } from "../../../utils/Date";
import { ReturnProps } from "../types";

export const AD_APTUS: ReturnProps<IAptus.IAptusBaseDocument> = {
  title: "Aptus.01.Advance",
  origin: "APTUS",
  type: "AD_APTUS",
  permission: "AD",
  approveItems: true,
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IAptus.IAptusBaseDocument) => row.Codigo || "-",
    },
    {
      name: "Favored",
      selector: (row: IAptus.IAptusBaseDocument) => row.Favorecido || "-",
    },
    {
      name: "CC",
      selector: (row: IAptus.IAptusBaseDocument) => row.CentroCusto || "-",
    },
    {
      name: "Value",
      selector: (row: IAptus.IAptusBaseDocument) => row.Valor || "-",
    },
    {
      name: "Date",
      selector: (row: IAptus.IAptusBaseDocument) =>
        dateBDtoRead(row.DataSolicitacao),
    },
    {
      name: "Lighthouse",
      selector: (row: IAptus.IAptusBaseDocument) => row.Critica || "-",
      cell: (row: IAptus.IAptusBaseDocument) =>
        row.Critica === "SIM" ? (
          <Notifications
            sx={{ color: "#E32027" }}
            fontSize="medium"
            titleAccess={row.CriticaMotivo}
          />
        ) : (
          "-"
        ),
    },
    {
      name: "Anomaly",
      cell: (row: IAptus.IAptusBaseDocument) => {
        return row?.Itens ? AptusService.handleAnomalyColumn(row?.Itens) : "-";
      },
    },
  ],
  detailModalHeader: (
    rows: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {row.Codigo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Requester.ID")}:`}</div>
            {`${row.Itens[0].idSolicitante} - ${row.Itens[0].Solicitante}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}:`}</div>
            {`${row.Itens[0].FavorecidoCPF} - ${row.Itens[0].Favorecido}`}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "Job.Position.Beneficiary"
            )}:`}</div>
            {row.Itens[0].Cargo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Area.Beneficiary")}:`}</div>
            {row.AreaRH}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CC")}:`}</div>
            {row.CentroCusto}
          </td>
          <td>
            <div className="tableTitle">{`${t("Order")}:`}</div>
            {row.OrdemInterna}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {`${row.Itens[0].Empresa} - ${row.Itens[0].EmpresaNome}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("CC.Description")}:`}</div>
            {row.Itens[0].CentroCustoDescricao}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {row.Itens[0].CidadeOrigem}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination")}:`}</div>
            {row.Itens[0].CidadeDestino}
          </td>
          <td>
            <div className="tableTitle">{`${t("Departure.Date")}:`}</div>
            {dateBDtoRead(row.Itens[0].DataInicioPeriodoViagem || "")}
          </td>
          <td>
            <div className="tableTitle">{`${t("Return.Date")}:`}</div>
            {dateBDtoRead(row.Itens[0].DataFimPeriodoViagem || "")}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Lighthouse")}:`}</div>
            {row.Itens[0].Critica === "SIM" ? (
              <Notifications
                fontSize="medium"
                color="error"
                titleAccess={row.Itens[0].CriticaMotivo}
              />
            ) : (
              "-"
            )}
          </td>
          <td>
            <div className="tableTitle">{`${t("Anomaly")}:`}</div>
            {row.Itens[0]?.Anomalia ? (
              <Warning
                fontSize="medium"
                color="error"
                titleAccess={row.Itens[0]?.Anomalia}
              />
            ) : (
              "-"
            )}
          </td>
          <td>
            <div className="tableTitle">{`${t("Justification")}:`}</div>
            {row.Itens[0].Objetivo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {row.Valor}
          </td>
        </tr>
      </tbody>
    );
  },
};
