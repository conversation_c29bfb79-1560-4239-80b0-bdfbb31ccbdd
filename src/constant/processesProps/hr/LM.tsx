import { IHRLearningManagement } from "@/interfaces/IHR";
import { HRService } from "@/services/hr";
import { t } from "i18next";
import { ReturnProps } from "..";

export const LM: ReturnProps<IHRLearningManagement> = {
  title: "Brf.Academy",
  origin: "HR",
  permission: "LM",
  type: "LM",
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row) => row.documentNumber || "",
    },
    {
      name: "Requester.ID",
      selector: (row) => row.userId || "",
    },
    {
      name: "Requester",
      selector: (row) => row.requesterName || "",
    },
    {
      name: "Title",
      selector: (row) => row.documentTitle || "",
    },
  ],
  detailModalHeader: async (
    rows: IHRLearningManagement | IHRLearningManagement[]
  ) => {
    const row = Array.isArray(rows) ? (rows[0] as IHRLearningManagement) : rows;

    const { data } = (await HRService.getDocumentDetail(
      "LM",
      row.documentNumber
    )) as { data: IHRLearningManagement };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {data.requesterName}
          </td>
          <td>
            <div className="tableTitle">{`${t("Type")}:`}</div>
            {`${data.documentType}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Title")}:`}</div>
            {`${data.documentTitle}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Start.Date")}:`}</div>
            {`${data.startDate}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("End.Date")}:`}</div>
            {`${data.endDate}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Description")}:`}</div>
            {`${data.description}`}
          </td>
        </tr>
      </tbody>
    );
  },
};
