import { ReturnProps } from "..";
import { ICommercial } from "../../../interfaces";

export const GCC: ReturnProps<ICommercial.TradeSpendProps> = {
  title: "Trade.Spend",
  origin: "COMMERCIAL",
  type: "GCC",
  permission: "GCC",
  hasDetailModal: false,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "ID",
      selector: (row: ICommercial.TradeSpendProps) =>
        row.recordDetail?.name || "-",
    },
    {
      name: "Creation.date",
      selector: (row: ICommercial.TradeSpendProps) => row.createdDate || "-",
    },
    {
      name: "Requester",
      selector: (row: ICommercial.TradeSpendProps) => row.requestor || "-",
    },
    {
      name: "Type",
      selector: (row: ICommercial.TradeSpendProps) =>
        row.approvalProcessName || "-",
    },
    {
      name: "Details",
      selector: (row: ICommercial.TradeSpendProps) =>
        row.recordDetail?.details || "-",
    },
    {
      name: "Client",
      selector: (row: ICommercial.TradeSpendProps) =>
        row.recordDetail?.customer || "-",
    },
    {
      name: "Amount.GCC",
      selector: (row: ICommercial.TradeSpendProps) =>
        row.recordDetail?.amount || "-",
    },
    {
      name: "More.Details.GCC",
      cell: (row: ICommercial.TradeSpendProps) =>
        row.recordLink ? (
          <a href={row.recordLink} target="_blank" rel="noopener noreferrer">
            {row.recordLink}
          </a>
        ) : (
          "-"
        ),
    },
  ],
};
