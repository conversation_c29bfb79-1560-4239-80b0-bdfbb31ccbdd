import { t } from "i18next";
import { ReturnProps } from "..";
import { ICommercial } from "../../../interfaces";
import { AtentionDivergencyIconButton, TableInternal } from "../styles";

export const SALES: ReturnProps<ICommercial.SalesProps> = {
  title: "Sales",
  origin: "COMMERCIAL",
  type: "SALES",
  permission: "SALES",
  hasDetailModal: false,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "Pre.Cap",
      selector: (row: ICommercial.SalesProps) => row.preCapNumber || "-",
    },
    {
      name: "Region.Sales",
      selector: (row: ICommercial.SalesProps) => row.region || "-",
    },
    {
      name: "Segmentation.Sales",
      selector: (row: ICommercial.SalesProps) => row.segmentacao || "-",
    },
    {
      name: "Chain.Name.Sales",
      selector: (row: ICommercial.SalesProps) => row.chain || "-",
    },
    {
      name: "Pre.Cap.Investment.Sales",
      selector: (row: ICommercial.SalesProps) => row.preCapTotalAmount || "-",
    },
    {
      name: "Pre.Cap.Rob.Sales",
      selector: (row: ICommercial.SalesProps) => row.rob || "-",
    },
    {
      name: "Segmentation.Budget.Sales",
      selector: (row: ICommercial.SalesProps) => row.poValue || "-",
    },
    {
      name: "Monthly.Inves.Sales",
      selector: (row: ICommercial.SalesProps) => row.accumulatedInvested || "-",
    },
    {
      name: "Quarter.Inves.Sales",
      selector: (row: ICommercial.SalesProps) =>
        row.InvestimentPercentage || "-",
    },
    {
      name: "Quarter.Product.Margin.Sales",
      selector: (row: ICommercial.SalesProps) =>
        row.ProductMarginPercentage || "-",
    },
    {
      name: "Alert.Investment",
      cell: (row: ICommercial.SalesProps) => {
        return row.alert ? <AtentionDivergencyIconButton type="button" /> : "-";
      },
    },
    {
      name: "Alert.Duplicated",
      cell: (row: ICommercial.SalesProps) => {
        const isRed = !!row.duplicated;

        return row.duplicated ? (
          <AtentionDivergencyIconButton type="button" isRed={isRed} />
        ) : (
          "-"
        );
      },
    },
    {
      name: "Status",
      selector: (row: ICommercial.SalesProps) => row.status || "-",
    },
  ],
  documentDetailHtml: (
    rows: ICommercial.SalesProps | ICommercial.SalesProps[]
  ) => {
    const row = Array.isArray(rows)
      ? (rows[0] as ICommercial.SalesProps)
      : rows;

    return (
      <TableInternal>
        <thead>
          <tr>
            <td style={{ width: "50%" }}>{t("Alert.Sales")}</td>
            <td style={{ width: "50%" }}>{t("Duplicated")}</td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{ width: "50%" }}>{row?.alert || "-"}</td>
            <td style={{ width: "50%" }}>{row?.duplicated || "-"}</td>
          </tr>
        </tbody>
        <thead>
          <tr>
            <td style={{ width: "50%" }}>{t("Investment.Analisys")}</td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{ width: "50%" }}>{row?.InvestmentAnalisys || "-"}</td>
          </tr>
        </tbody>
      </TableInternal>
    );
  },
};
