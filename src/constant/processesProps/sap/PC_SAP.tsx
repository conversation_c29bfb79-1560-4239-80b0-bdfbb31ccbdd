import { ISap } from "@/interfaces";
import { getAdditionalField } from "@/utils/GetItemsSap";
import { ReturnProps } from "..";

export const PC_SAP: ReturnProps<ISap.ItemProps> = {
  title: "CockpitPCLD.Requests",
  origin: "SAP",
  type: "PC_SAP",
  permission: "PC_SAP",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "PC.Company.Market.PCLD",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Month",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PERIODO", row.adicionais) || "-";
      },
    },
    {
      name: "User.NC",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("USUARIO", row.adicionais) || "-";
      },
    },
    {
      name: "Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA", row.adicionais) || "-";
      },
    },
    {
      name: "PC.Time",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("HORA", row.adicionais) || "-";
      },
    },
  ],
};

