import { Box } from "@mui/material";
import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { BoldHeader } from "../styles";

export const NC: ReturnProps<ISap.ItemProps> = {
  title: "PNC.Digital.NC",
  origin: "SAP",
  type: "NC",
  permission: "NC",
  hasDetailModal: true,
  hasDetailRoute: true,
  headerColumns: [
    {
      name: "Non.Conformity.Sequence.NC",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Non.Conformity.Issue.Date.NC",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Plant.NC",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Vendor.NC",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Material.NC",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Reimbursement.Value.NC",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    const rowDetail = Array.isArray(row) ? (row[1] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{t("Non.Conformity.Sequence.NC")}:</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">
              {t("Non.Conformity.Issue.Date.NC")}:
            </div>
            {rowHeader?.data_emissao || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("Plant.NC")}:</div>
            {rowHeader?.CENTRO || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("Vendor.NC")}:</div>
            {rowHeader?.fornecedor || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("Material.NC")}:</div>
            {rowHeader?.material || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{t("Purchase.Order.NC")}:</div>
            {getAdditionalField("PEDIDO", rowDetail.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("Nota.Fiscal.Number.NC")}:</div>
            {getAdditionalField("NUM_NF", rowDetail.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("User.NC")}:</div>
            {rowDetail.requisitante || "-"}
          </td>
          <td>
            <div className="tableTitle">{t("Reimbursement.Value.NC")}:</div>
            {rowHeader?.vldoc || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    if (Array.isArray(rows) && rows.length > 1) {
      rows = rows.filter((_, idx) => idx !== 0);
    }

    if (Array.isArray(rows) && rows.length) {
      return (
        <>
          <tr className="detailModalContent">
            <BoldHeader style={{ paddingTop: "20px" }} colSpan={3}>
              {t("Non.Conformity.Items.NC")}
            </BoldHeader>
          </tr>
          {rows.map((row: ISap.ItemProps) => {
            const calcFormValue = getAdditionalField(
              "CALCFORM",
              row.adicionais
            );

            return (
              <Box
                key={`modal-table-internal-${row.documento}-${row.item}`}
                borderBottom={1}
                borderColor="rgba(0, 0, 0, 0.12)"
              >
                <tr key={row?.item}>
                  <td>
                    <span>{t("Non.Conformity.Item.NC")}</span>
                    {row?.item ?? "-"}
                  </td>
                  <td>
                    <span>{t("Material.Detailed.Problem.NC")}</span>
                    {getAdditionalField("PROB_DET", row?.adicionais) || "-"}
                  </td>
                  <td>
                    <span>{t("Standard.Value")}</span>
                    {getAdditionalField("STDVAL", row?.adicionais) || "-"}
                  </td>
                  <td>
                    <span>{t("Measured.Value")}</span>
                    {getAdditionalField("RESULTCOMP", row?.adicionais) || "-"}
                  </td>
                  <td>
                    <span>{t("Limit.Value")}</span>
                    {getAdditionalField("LIMITVAL", row?.adicionais) || "-"}
                  </td>
                  <td>
                    <span>{t("Standard.Discount")}</span>
                    {getAdditionalField("STDDISCOUNT", row?.adicionais) || "-"}
                  </td>
                  <td>
                    <span>{t("Reimbursement.Value.NC")}</span>
                    {row?.vlitem ?? "-"}
                  </td>
                </tr>
                {calcFormValue && (
                  <tr>
                    <td colSpan={7}>
                      <span
                        style={{
                          fontSize: "14px",
                          color: "rgba(0,0,0,0.54)",
                        }}
                      >
                        {t("Discount.format")}
                      </span>
                      {calcFormValue}
                    </td>
                  </tr>
                )}
              </Box>
            );
          })}
        </>
      );
    }
  },
};
