import { IDocusign } from "@/interfaces";
import Iframe from "react-iframe";
import { ReturnProps } from "..";

export function getAdditionalField(
  value: string,
  additionalArray?: IDocusign.IAdicionalProps[]
): string {
  if (!additionalArray?.length || !value) {
    return "";
  }
  const field = additionalArray.find((item) => item.CAMPO === value);
  return field?.VALOR !== undefined ? String(field.VALOR) : "";
}

export const DS: ReturnProps<any> = {
  title: "Pending.Sign.Document",
  origin: "DOCUSIGN",
  type: "DS",
  permission: "DS",
  additional1: (row: IDocusign.ItemProps) => {
    return getAdditionalField("ENVELOPEID", row.ADICIONAIS) || "";
  },
  additional2: (row: IDocusign.ItemProps) => {
    return getAdditionalField("RECIPIENTID", row.ADICIONAIS) || "";
  },
  headerColumns: [
    {
      name: "File.name",
      selector: (row: IDocusign.ItemProps) =>
        getAdditionalField("SUBJECT", row.ADICIONAIS) || "",
    },
    {
      name: "Sender",
      selector: (row: IDocusign.ItemProps) =>
        getAdditionalField("SENDER_USERNAME", row.ADICIONAIS) || "",
    },
    {
      name: "Send.date",
      selector: (row: IDocusign.ItemProps) =>
        getAdditionalField("SENTDATE", row.ADICIONAIS) || "",
    },
    {
      name: "Origin",
      selector: (row: IDocusign.ItemProps) =>
        getAdditionalField("CONTA", row.ADICIONAIS) || "",
    },
    {
      name: "Status.of.the.envelope",
      selector: (row: IDocusign.ItemProps) =>
        getAdditionalField("STATUS_ENVELOPE", row.ADICIONAIS) || "",
    },
  ],
  detailColumns: [
    {
      name: "Name",
      selector: (row: IDocusign.SignersProps) => row.NAME || "-",
    },
    {
      name: "Status",
      selector: (row: IDocusign.SignersProps) => row.STATUS || "-",
    },
  ],
  hasDetailModal: true,
  detailModalContent: (rows: IDocusign.ItemProps[]) => {
    const rowDetail = Array.isArray(rows)
      ? (rows[0] as IDocusign.ItemProps)
      : rows;

    const routeDocuSign = `${getAdditionalField(
      "URL_1",
      rowDetail.ADICIONAIS
    )}${getAdditionalField("URL_2", rowDetail.ADICIONAIS)}`;

    const height = window.innerHeight - 120;
    const heightpx = `${height}px`;

    return (
      <Iframe
        url={routeDocuSign}
        width="100%"
        height={heightpx}
        id="myId"
        styles={{
          display: "block !important",
          zIndex: "-1 !important",
        }}
      />
    );
  },
};
