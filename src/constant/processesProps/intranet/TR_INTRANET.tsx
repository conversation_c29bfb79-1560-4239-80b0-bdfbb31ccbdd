import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { dateUStoBR } from "@/utils/Date";
import { t } from "i18next";
import { ReturnProps } from "..";
import { putDecimalPoint } from "../../../utils/Currency";

export const TR_INTRANET: ReturnProps<IIntranet.ItemProps> = {
  title: "Tributes",
  origin: "INTRANET",
  type: "TR_INTRANET",
  permission: "TR",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (row: IIntranet.ItemProps) => row.CODIGO || "-",
    },
    {
      name: t("Beneficiary"),
      selector: (row: IIntranet.ItemProps) => row.FAVORECIDO || "-",
    },
    {
      name: t("Favored.Name"),
      selector: (row: IIntranet.ItemProps) => row.NOME || "-",
    },
    {
      name: t("CC"),
      selector: (row: IIntranet.ItemProps) => row.CC?.replace(/\$/g, "") || "-",
    },
    {
      name: t("Value"),
      selector: (row: IIntranet.ItemProps) => putDecimalPoint(row.VALOR) || "-",
    },
    {
      name: t("Date"),
      selector: (row: IIntranet.ItemProps) =>
        dateUStoBR(row.DATASOLICITACAO) || "-",
    },
    {
      name: t("Expiry.Date"),
      selector: (row: IIntranet.ItemProps) =>
        dateUStoBR(row.DATAVENCIMENTO) || "-",
    },
  ],
  detailModalHeader: async (
    rows: IIntranet.ItemProps | IIntranet.ItemProps[]
  ) => {
    const row = Array.isArray(rows) ? (rows[0] as IIntranet.ItemProps) : rows;

    if (!row || !row.CODIGO) {
      throw new Error("Invalid row or missing CODIGO property.");
    }

    const { data: dataItems } = await api.get(
      `http://localhost:5252/TributeItems/${row.CODIGO}`,
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Cors: "*",
          Authorization: `Bearer ${localStorage.getItem("employeeToken")}`,
        },
      }
    );
    const { data: dataDetails } = await api.get(
      `http://localhost:5252/TributeDetail/${row.CODIGO}`,
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Cors: "*",
          Authorization: `Bearer ${localStorage.getItem("employeeToken")}`,
        },
      }
    );

    const tempData = dataDetails.DATA[0];

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.TRI_CODIGO}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Type")}:`}</div>
            {tempData.TRI_TIPO.toString() === "2"
              ? t("Expenses.and.Contributions")
              : t("Taxes")}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("CPF.CNPJ.Name")}:`}</div>
            {`${tempData.TRI_CPFCNPJ} -${tempData.TRI_NOME}`}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {`${tempData.TRI_COD_EMPRESA} -${tempData.TRI_NOME_EMPRESA}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Amount")}:`}</div>
            {`${tempData.TRI_MOEDA ? tempData.TRI_MOEDA : ""} ${putDecimalPoint(
              tempData.TRI_VALOR
            )}`}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {`${tempData.TRI_USR_SIGLA_CAD} -${tempData.TRI_USR_NOME_CAD}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Slip.Number")}:`}</div>
            {tempData.TRI_NUM_GUIA}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Launched.on")}:`}</div>
            {dateUStoBR(tempData.TRI_DATA_CAD_TRIB)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Competence")}:`}</div>
            {tempData.TRI_COMPETENCIA}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Expiry.Date")}:`}</div>
            {dateUStoBR(tempData.TRI_DATA_VENC)}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Observation")}:`}</div>
            {tempData.TRI_OBS}
          </td>
        </tr>
        <tr>
          <td colSpan={5}>
            <div className="modal-title">{t("Items")}</div>
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Tributes.Taxes")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Division")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Amount")}:`}</div>
          </td>
        </tr>
        {dataItems.DATA?.map((item: IIntranet.TrDetailItem) => {
          return (
            <tr key={`${item.TRIT_DESCRICAO}-${item.TRII_VALOR.toString()}`}>
              <td>{item.TRIT_DESCRICAO}</td>
              <td>{item.TRII_CCUSTO}</td>
              <td>{item.TRII_DIVISAO}</td>
              <td>{putDecimalPoint(item.TRII_VALOR.toString())}</td>
            </tr>
          );
        })}
      </tbody>
    );
  },
};
