import {
  Autocomplete,
  CircularProgress,
  FormControl,
  TextField,
} from "@mui/material";
import { t } from "i18next";
import React from "react";
import { ISearchUser } from "../../interfaces";

interface SelectProps {
  value: string;
  options: ISearchUser.SearchUsersAccounts[];
  onChange: (value: ISearchUser.SearchUsersAccounts[]) => void;
  onInputChange: (value: string) => void;
  label?: string;
  disabled?: boolean;
  isLoadingOptions?: boolean;
  loadMore?: boolean;
  setLoadMore?: (value: boolean) => void;
  loadMoreResults?: () => void;
  resetValues?: () => void;
}

export const Select: React.FC<SelectProps> = ({
  value,
  options,
  onChange,
  disabled = false,
  onInputChange,
  label,
  isLoadingOptions,
  loadMore,
  setLoadMore,
  loadMoreResults,
  resetValues,
}) => {
  const selectedValue = React.useMemo(
    () => options.find((option) => option.name === value) || null,
    [options, value]
  );

  const [hasSearched, setHasSearched] = React.useState<boolean>(false);
  const [inputLength, setInputLength] = React.useState<number>(0);

  // Reset hasSearched when options change from empty to filled or vice versa
  React.useEffect(() => {
    if (options.length > 0) {
      setHasSearched(true);
    }
  }, [options]);

  return (
    <FormControl fullWidth>
      <Autocomplete
        options={options}
        disabled={disabled}
        loading={isLoadingOptions}
        getOptionLabel={(option) => `${option.initials} - ${option.name}` || ""}
        isOptionEqualToValue={(option, value) => option.id === value?.id}
        value={selectedValue || null}
        open={
          inputLength >= 3 ||
          isLoadingOptions ||
          (options.length > 0 && hasSearched)
        }
        loadingText={
          isLoadingOptions ? t("Loading", "Carregando...") : undefined
        }
        onChange={(_, newValue) => {
          onChange(newValue ? [newValue] : []);
        }}
        onInputChange={(_, newValue, reason) => {
          if (reason === "input") {
            onInputChange(newValue);
            setInputLength(newValue.length);

            // Marca que o usuário fez uma busca quando digitar 3+ caracteres
            if (newValue.length >= 3) {
              setHasSearched(true);
            } else if (newValue.length === 0) {
              // Reset quando limpar o campo
              setHasSearched(false);
            }
          }
        }}
        filterOptions={(options, state) => {
          if (!state.inputValue || state.inputValue.length < 3) {
            return [];
          }
          if (isLoadingOptions) {
            return options;
          }

          return options;
        }}
        autoComplete
        noOptionsText={
          hasSearched &&
          inputLength >= 3 &&
          !isLoadingOptions &&
          options.length === 0
            ? t("No.options.available", "Nenhuma opção disponível")
            : ""
        }
        // clearOnEscape
        // onClose={() => {
        //   // onChange([]);
        //   onInputChange("");
        //   setHasSearched(false); // Reset quando fechar
        //   setInputLength(0);
        //   resetValues && resetValues();
        // }}
        slotProps={{
          listbox: {
            onScroll: (event) => {
              if (loadMore && !isLoadingOptions && loadMoreResults) {
                const listboxNode = event.currentTarget;
                // Verifica se chegou próximo ao final da lista (dentro de 10px)
                const isNearBottom =
                  listboxNode.scrollTop + listboxNode.offsetHeight >=
                  listboxNode.scrollHeight - 10;

                if (isNearBottom) {
                  loadMoreResults();
                  setLoadMore && setLoadMore(false); // Evita múltiplas chamadas
                }
              }
            },
          },
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            variant="outlined"
            placeholder={t("Search.by.ID.Acronym.Name")}
            slotProps={{
              input: {
                ...params.InputProps,
                endAdornment: (
                  <React.Fragment>
                    {isLoadingOptions ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </React.Fragment>
                ),
              },
            }}
          />
        )}
      />
    </FormControl>
  );
};
