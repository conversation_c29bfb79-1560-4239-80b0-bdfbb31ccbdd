import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import {
  IProcurement,
  IProcurementApprovalReprovalParams,
} from "@/interfaces/procurement";
import React, { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ProcurementService } from "../../services";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import { SecondaryDataTable } from "../DataTable/SecondaryDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableProcurement: FC<{
  headerData: IProcurement[] | any;
  detailData?: IProcurement[] | any;
  type: string;
}> = ({ headerData, detailData, type }) => {
  const {
    headerColumns,
    detailColumns,
    documentDetailHtml,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    approveItems,
    origin,
  } = processesProps(type)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IProcurement | IProcurement[];
  }>({ open: false, data: [] as IProcurement[] });
  const [expandedRows, setExpandedRows] = useState<IProcurement[]>([]);

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: IProcurement | null;
  }>({
    open: false,
    type: null,
    row: null,
  });

  const handleRowExpansion = useCallback(
    (_row: IProcurement) => {
      if (documentDetailHtml) {
        const content = documentDetailHtml(detailData);
        if (React.isValidElement(content)) {
          return content;
        } else if (content) {
          // Se não for um elemento React, mas tiver conteúdo, envolvê-lo em um div
          return <div style={{ padding: "16px" }}>{content}</div>;
        }
      }
      return null;
    },
    [documentDetailHtml]
  );

  const handleOpenModal = (data: IProcurement | IProcurement[]) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as IProcurement[] });
  };

  const handleModalApprove = async (data: IProcurement) => {
    await handleApprovalReprovalDocument(data, 1);
    handleCloseModal();
  };

  const handleModalReject = (data: IProcurement) => {
    handleOpenApprovalModal(data, "disapprove");
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: IProcurement,
    type: "approve" | "disapprove"
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
    });
  };

  const handleCloseApprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (comment: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status = approvalModalState.type === "approve" ? 1 : 0;

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        comment
      );
      handleCloseApprovalModal();
    }
  };

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<IProcurementApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (row: IProcurement, operation?: 0 | 1, comment?: string) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process: type,
        status: operation as number,
        reason: comment,
      });
    },
    [type, origin]
  );

  const renderExpandableRowsComponent = useCallback(
    ({ data }: { data: IProcurement }) => {
      const row = data as IProcurement;
      if (!detailData?.length || !detailColumns) return null;

      const documentDetails = ProcurementService.filterDocumentDetails(
        detailData,
        String(row.numeroSolicitacao ?? row.id ?? row.Documento)
      );

      return (
        <SecondaryDataTable
          columns={detailColumns as TableColumn<IProcurement>[]}
          data={documentDetails}
        />
      );
    },
    [detailData, detailColumns, approveItems]
  );

  const handleDataToModal = useCallback((row: IProcurement) => {
    handleOpenModal(row);
  }, []);

  const addActionColumn = useCallback(
    (
      columns: TableColumn<IProcurement>[],
      hasDetailModal: boolean,
      isDetail: boolean = false
    ) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "auto",
          minWidth: "100px",
          maxWidth: "180px",
          cell: (row: IProcurement) => {
            return (
              !user.onlyReading && (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                    padding: "2px",
                  }}
                >
                  <ActionButtons<IProcurement>
                    row={row}
                    onApprove={async () => {
                      handleApprovalReprovalDocument(row, 1);
                    }}
                    onDisapprove={() => {
                      handleOpenApprovalModal(row, "disapprove");
                    }}
                    showOpenModalButton={!isDetail && hasDetailModal}
                    onOpenModal={() => handleDataToModal(row)}
                    showOpenUrlButton={false}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      user.onlyReading,
      type,
    ]
  );

  addActionColumn(
    headerColumns as TableColumn<IProcurement>[],
    !!hasDetailModal
  );

  return (
    <div>
      <MainDataTable
        expandedRows={expandedRows}
        rowKey={(row: IProcurement) => row.numeroSolicitacao || row.id}
        columns={headerColumns as TableColumn<IProcurement>[]}
        data={headerData}
        expandableRows={!!detailData?.length}
        onRowExpandToggled={(expanded: boolean, row: IProcurement) => {
          if (expanded) {
            setExpandedRows((prev) => [...prev, row]);
          } else {
            setExpandedRows((prev) =>
              prev.filter(
                (expandedRow) =>
                  (expandedRow.numeroSolicitacao &&
                    expandedRow.numeroSolicitacao !== row.numeroSolicitacao) ||
                  (expandedRow.id && expandedRow.id !== row.id)
              )
            );
          }
        }}
        expandableRowExpanded={(row) =>
          expandedRows.some(
            (expanded) =>
              (expanded.numeroSolicitacao &&
                expanded.numeroSolicitacao === row.numeroSolicitacao) ||
              (expanded.id && expanded.id === row.id)
          )
        }
        expandableRowsComponent={({ data }) => {
          const customContent = handleRowExpansion(data);

          if (customContent) {
            return customContent;
          }

          return detailColumns ? renderExpandableRowsComponent({ data }) : null;
        }}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            detailModalContent={detailModalContent}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor={"error"}
        isRequired={approvalModalState.type === "disapprove"}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
