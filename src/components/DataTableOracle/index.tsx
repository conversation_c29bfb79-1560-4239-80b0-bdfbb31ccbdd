import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";

import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { IOracle } from "../../interfaces";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableOracle: FC<{
  headerData: IOracle.OracleItemType[];
  type: string;
}> = ({ headerData, type }) => {
  const {
    headerColumns,
    documentDetailHtml,
    origin,
    title,
    detailModalHeader,
  } = processesProps(type)[0];

  const { user } = useAuth();
  const { t } = useTranslation();

  const [expandedRows, setExpandedRows] = useState<IOracle.OracleItemType[]>(
    []
  );
  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IOracle.OracleItemType | IOracle.OracleItemType[];
  }>({ open: false, data: [] as IOracle.OracleItemType[] });

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: IOracle.OracleItemType | null;
  }>({
    open: false,
    row: null,
  });

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<IOracle.ApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: IOracle.OracleItemType,
      status: "APPROVED" | "REJECTED",
      reason?: string
    ) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process: type,
        status,
        reason,
      });
    },
    [headerData]
  );

  const handleOpenModal = (
    data: IOracle.OracleItemType | IOracle.OracleItemType[]
  ) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as IOracle.OracleItemType[] });
  };

  const handleOpenDisapprovalModal = (row: IOracle.OracleItemType) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (reason: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "REJECTED",
        reason
      );
      handleCloseDisapprovalModal();
    }
  };

  const handleModalApprove = async (
    data: IOracle.OracleItemType | IOracle.OracleItemType[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    await handleApprovalReprovalDocument(row, "APPROVED");
    handleCloseModal();
  };

  const handleModalReject = async (
    data: IOracle.OracleItemType | IOracle.OracleItemType[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenDisapprovalModal(row);
    handleCloseModal();
  };

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (columns: TableColumn<IOracle.OracleItemType>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: IOracle.OracleItemType) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<IOracle.OracleItemType>
                    row={row}
                    onApprove={() =>
                      handleApprovalReprovalDocument(row, "APPROVED")
                    }
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    showOpenModalButton={!!detailModalHeader}
                    onOpenModal={() => handleOpenModal(row)}
                    showOpenUrlButton={false}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [handleApprovalReprovalDocument, user.onlyReading]
  );

  addActionColumn(headerColumns as TableColumn<IOracle.OracleItemType>[]);

  const handleRowExpansion = useCallback(
    (row: IOracle.OracleItemType) => {
      if (documentDetailHtml) {
        try {
          const content = documentDetailHtml(row);
          return (
            <div
              style={{
                padding: "16px",
                position: "relative",
              }}
              data-row-id={row.id}
            >
              {content}
            </div>
          );
        } catch (error) {
          console.error("Error rendering expandable content:", error);
          return (
            <div style={{ padding: "16px", color: "red" }}>
              Erro ao exibir detalhes. Consulte o console para mais informações.
            </div>
          );
        }
      }
      return null;
    },
    [documentDetailHtml]
  );

  return (
    <div className="oracle-data-table-container">
      <MainDataTable
        key={`oracle-table-${type}`}
        expandedRows={expandedRows}
        rowKey={(row: IOracle.OracleItemType) => row.id}
        columns={headerColumns as TableColumn<IOracle.OracleItemType>[]}
        data={headerData}
        expandableRows={Boolean(documentDetailHtml)}
        onRowExpandToggled={(expanded, row) => {
          if (expanded) {
            setExpandedRows((prev) => [...prev, row]);
          } else {
            setExpandedRows((prev) =>
              prev.filter((expandedRow) => expandedRow.id !== row.id)
            );
          }
        }}
        expandableRowExpanded={(row) =>
          expandedRows.some((expanded) => expanded.id === row.id)
        }
        expandableRowsComponent={({ data }) => {
          // Only render expandable content if documentDetailHtml exists and row is expanded
          if (
            documentDetailHtml &&
            expandedRows.some((expanded) => expanded.id === data.id)
          ) {
            return handleRowExpansion(data);
          }
          return null;
        }}
      />
      {modalState.open && modalState.data && detailModalHeader && (
        <ModalItemDetails
          open={modalState.open}
          onClose={handleCloseModal}
          modalTitle={title}
          data={modalState.data}
          detailModalHeader={detailModalHeader}
          origin={origin}
          onApprove={handleModalApprove}
          onReject={handleModalReject}
        />
      )}

      <ModalApprovalReason
        open={disapprovalModalState.open}
        onClose={handleCloseDisapprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmDisapprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
