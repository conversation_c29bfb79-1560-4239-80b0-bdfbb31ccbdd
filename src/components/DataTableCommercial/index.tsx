import { FC, useCallback, useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ICommercial } from "../../interfaces";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";

import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { DataTableBaseProps } from "@/interfaces/dataTable";

export const DataTableCommercial: FC<
  DataTableBaseProps<ICommercial.ICommercialProps>
> = ({ headerData, process = "" }) => {
  const { headerColumns, documentDetailHtml, origin } =
    processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    setTableData(headerData);
  }, [headerData]);

  const [tableData, setTableData] =
    useState<ICommercial.ICommercialProps[]>(headerData);

  const [expandedRows, setExpandedRows] = useState<
    ICommercial.ICommercialProps[]
  >([]);

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: ICommercial.ICommercialProps | null;
  }>({
    open: false,
    row: null,
  });

  const handleOpenDisapprovalModal = (row: ICommercial.ICommercialProps) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (comentary: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "R",
        comentary
      );
      handleCloseDisapprovalModal();
    }
  };

  const { approveReproveDocument, isLoadingApproval } = useApprovalDocument<
    ICommercial.IGCCParams | ICommercial.IPreCapParams
  >();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: ICommercial.ICommercialProps,
      status: "A" | "R",
      comment?: string
    ) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason: comment,
      });
    },
    [process, origin]
  );

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (columns: TableColumn<ICommercial.ICommercialProps>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: ICommercial.ICommercialProps) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<ICommercial.ICommercialProps>
                    row={row}
                    onApprove={() => handleApprovalReprovalDocument(row, "A")}
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    showOpenUrlButton={false}
                    showOpenModalButton={false}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [handleApprovalReprovalDocument, user.onlyReading]
  );

  addActionColumn(headerColumns as TableColumn<ICommercial.ICommercialProps>[]);

  const handleRowExpansion = useCallback(
    (row: ICommercial.SalesProps) => {
      if (documentDetailHtml) {
        try {
          const content = documentDetailHtml(row);
          return (
            <div
              style={{
                padding: "16px",
                position: "relative",
              }}
              data-row-id={row.preCapNumber}
            >
              {content}
            </div>
          );
        } catch (error) {
          console.error("Error rendering expandable content:", error);
          return (
            <div style={{ padding: "16px", color: "red" }}>
              Erro ao exibir detalhes. Consulte o console para mais informações.
            </div>
          );
        }
      }
      return null;
    },
    [documentDetailHtml]
  );

  return (
    <div className="commercial-data-table-container">
      <MainDataTable
        key={`commercial-table-${process}`}
        expandedRows={expandedRows}
        rowKey={(row: ICommercial.ICommercialProps) => row.preCapNumber}
        columns={headerColumns as TableColumn<ICommercial.ICommercialProps>[]}
        data={tableData}
        expandableRows={Boolean(documentDetailHtml)}
        onRowExpandToggled={(expanded, row) => {
          if (expanded) {
            setExpandedRows((prev) => {
              if (
                !prev.some(
                  (expandedRow) => expandedRow.preCapNumber === row.preCapNumber
                )
              ) {
                return [...prev, row];
              }
              return prev;
            });
          } else {
            setExpandedRows((prev) =>
              prev.filter(
                (expandedRow) => expandedRow.preCapNumber !== row.preCapNumber
              )
            );
          }
        }}
        expandableRowsComponent={({ data }) => {
          if (
            documentDetailHtml &&
            expandedRows.some(
              (expandedRow) => expandedRow.preCapNumber === data.preCapNumber
            )
          ) {
            return handleRowExpansion(data);
          }
          return null;
        }}
      />

      <ModalApprovalReason
        open={disapprovalModalState.open}
        onClose={handleCloseDisapprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmDisapprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
