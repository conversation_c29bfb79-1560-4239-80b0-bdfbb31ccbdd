import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { APIServiceNow } from "../../api";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { IServiceNow } from "../../interfaces";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableServiceNow: FC<{
  headerData: IServiceNow.ItemProps[];
  process: string;
}> = ({ headerData, process }) => {
  const { headerColumns, origin, title } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const { isLoading: isLoadingModal, mutate } = useMutation(
    async (documentNumber: string) => {
      const resp = await APIServiceNow.getDocumentDetails(documentNumber);
      return resp;
    }
  );

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IServiceNow.ItemProps | IServiceNow.ItemProps[];
    row: IServiceNow.ItemProps | null;
  }>({ open: false, data: [] as IServiceNow.ItemProps[], row: null });

  const handleOpenModal = (
    data: IServiceNow.ItemProps | IServiceNow.ItemProps[],
    row: IServiceNow.ItemProps | null
  ) => {
    setModalState({ open: true, data, row });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [], row: null });
  };

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: IServiceNow.ItemProps | null;
  }>({
    open: false,
    row: null,
  });

  const handleOpenDisapprovalModal = (row: IServiceNow.ItemProps) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (comment: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "R",
        comment
      );
      handleCloseDisapprovalModal();
    }
  };

  const handleModalApprove = async (data: IServiceNow.ItemProps) => {
    await handleApprovalReprovalDocument(data, "A");
    handleCloseModal();
  };

  const handleModalReject = (data: IServiceNow.ItemProps) => {
    handleOpenDisapprovalModal(data);
    handleCloseModal();
  };

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<IServiceNow.ApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (row: IServiceNow.ItemProps, status: "A" | "R", comment?: string) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason: comment,
      });
    },
    [process, origin]
  );

  const handleDataToModal = async (row: IServiceNow.ItemProps) => {
    const documentNumber = row.Registro;

    try {
      mutate(documentNumber, {
        onSuccess(data) {
          if (data.success) {
            handleOpenModal(data.data, row);
          }
        },
      });
    } catch (error) {
      console.error("Error fetching document details:", error);
    }
  };

  const addActionColumn = useCallback(
    (columns: TableColumn<IServiceNow.ItemProps>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "185px",
          maxWidth: "185px !important",
          cell: (row: IServiceNow.ItemProps) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<IServiceNow.ItemProps>
                    row={row}
                    onApprove={() => handleApprovalReprovalDocument(row, "A")}
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    onOpenModal={() => handleDataToModal(row)}
                    openUrl={row.Link}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [handleApprovalReprovalDocument, user.onlyReading]
  );

  addActionColumn(headerColumns as TableColumn<IServiceNow.ItemProps>[]);

  return (
    <div className="servicenow-data-table-container">
      <MainDataTable
        rowKey={(row: IServiceNow.ItemProps) => row.Registro}
        columns={headerColumns as TableColumn<IServiceNow.ItemProps>[]}
        data={headerData}
      />
      {modalState.open && modalState.data && (
        <ModalItemDetails
          open={modalState.open}
          onClose={handleCloseModal}
          modalTitle={title}
          origin={origin}
          data={modalState.data}
          row={modalState.row || ({} as IServiceNow.ItemProps)}
          onApprove={handleModalApprove}
          onReject={handleModalReject}
        />
      )}

      <ModalApprovalReason
        open={disapprovalModalState.open}
        onClose={handleCloseDisapprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmDisapprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />

      <Loading open={isLoadingApproval || isLoadingModal} />
    </div>
  );
};
