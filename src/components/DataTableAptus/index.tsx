import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";

import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { IAptus } from "../../interfaces";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableAptus: FC<{
  headerData: IAptus.IAptusBaseDocument[];
  process: string;
  type: string;
}> = ({ headerData, process, type }) => {
  const { headerColumns, origin, title, hasDetailModal, detailModalHeader } =
    processesProps(type)[0] || {};

  const uniqueTableId = `aptus-table-${type}-${process}`;

  const { user } = useAuth();
  const { t } = useTranslation();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[];
  }>({ open: false, data: [] as IAptus.IAptusBaseDocument[] });

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: IAptus.IAptusBaseDocument | null;
  }>({
    open: false,
    row: null,
  });

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<any>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: IAptus.IAptusBaseDocument,
      status: "1" | "2",
      comentary?: string
    ) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process: type,
        status,
        reason: comentary,
      });
    },
    [type, origin]
  );

  const handleOpenModal = (
    data: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as IAptus.IAptusBaseDocument[] });
  };

  const handleModalApprove = async (
    data: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    await handleApprovalReprovalDocument(row, "1");
    handleCloseModal();
  };

  const handleModalReject = (
    data: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenDisapprovalModal(row);
    handleCloseModal();
  };

  const handleOpenDisapprovalModal = (row: IAptus.IAptusBaseDocument) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (comentary: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "2",
        comentary
      );
      handleCloseDisapprovalModal();
    }
  };

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (
      columns: TableColumn<IAptus.IAptusBaseDocument>[],
      hasDetailModal: boolean
    ) => {
      const actionsColumnExists = columns?.some(
        (column) => column?.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: IAptus.IAptusBaseDocument) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<IAptus.IAptusBaseDocument>
                    row={row}
                    onApprove={() => handleApprovalReprovalDocument(row, "1")}
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    showOpenUrlButton={false}
                    showOpenModalButton={hasDetailModal}
                    onOpenModal={() => handleOpenModal(row)}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleApprovalReprovalDocument,
      handleOpenDisapprovalModal,
      user.onlyReading,
      handleOpenModal,
    ]
  );

  addActionColumn(
    headerColumns as TableColumn<IAptus.IAptusBaseDocument>[],
    !!hasDetailModal
  );

  return (
    <div className={`aptus-data-${uniqueTableId}`}>
      <MainDataTable
        key={uniqueTableId}
        rowKey={(row: IAptus.IAptusBaseDocument) => row.Codigo}
        columns={headerColumns as TableColumn<IAptus.IAptusBaseDocument>[]}
        data={headerData}
      />
      {modalState.open && modalState.data && detailModalHeader && (
        <ModalItemDetails
          key={`modal-${uniqueTableId}`}
          open={modalState.open}
          onClose={handleCloseModal}
          modalTitle={title}
          data={modalState.data}
          detailModalHeader={detailModalHeader}
          origin={origin}
          onApprove={handleModalApprove}
          onReject={handleModalReject}
        />
      )}

      <ModalApprovalReason
        open={disapprovalModalState.open}
        onClose={handleCloseDisapprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmDisapprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
