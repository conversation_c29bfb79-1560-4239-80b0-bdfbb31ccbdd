import { ItemDetailProps } from "@/interfaces/onelog";
import useOnelogStore from "@/stores/onelogStore";
import {
  Box,
  MenuItem,
  Select,
  SelectChangeEvent,
  useMediaQuery,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { t } from "i18next";
import React from "react";

interface ReasonSubReasonModalTrProps {
  row: ItemDetailProps;
}

const ReasonSubReasonModalTr: React.FC<ReasonSubReasonModalTrProps> = ({
  row,
}) => {
  const { reason, setReason, subReason, setSubReason } = useOnelogStore();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <tr>
      {isMobile ? (
        <>
          <td colSpan={3}>
            <span className="tableTitle">{`${t("Reason")}:`}</span>
            <Box sx={{ minWidth: 200, mt: 1 }}>
              <Select
                value={reason || ""}
                onChange={(event: SelectChangeEvent<string>) =>
                  setReason(event.target.value)
                }
                fullWidth
                size="small"
                displayEmpty
              >
                {row?.motivos?.map((item, idx) => (
                  <MenuItem
                    key={`${item?.descricao}-${idx}`}
                    value={item.descricao}
                  >
                    {item?.descricao}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </td>
          <tr>
            <td colSpan={3}>
              <span className="tableTitle">{`${t("Subreason")}:`}</span>{" "}
              <Box sx={{ minWidth: 200, mt: 1 }}>
                <Select
                  value={
                    row?.motivos
                      ?.find((item) => item.descricao === reason)
                      ?.subMotivos.some((item) => item.descricao === subReason)
                      ? subReason
                      : ""
                  }
                  onChange={({ target }) => {
                    setSubReason(target.value);
                  }}
                  fullWidth
                  size="small"
                >
                  <MenuItem key="null" value="">
                    Selecione
                  </MenuItem>
                  {row?.motivos
                    ?.find((item) => item.descricao === reason)
                    ?.subMotivos.map((item, idx) => (
                      <MenuItem
                        key={`${item?.descricao}${idx}`}
                        value={item.descricao}
                      >
                        {item.descricao}
                      </MenuItem>
                    ))}
                </Select>
              </Box>
            </td>
          </tr>
        </>
      ) : (
        <>
          <td colSpan={1}>
            <span className="tableTitle">{`${t("Reason")}:`}</span>
            <Box sx={{ minWidth: 200 }}>
              <Select
                value={reason || ""}
                onChange={(event: SelectChangeEvent<string>) =>
                  setReason(event.target.value)
                }
                fullWidth
                size="small"
                displayEmpty
              >
                {row?.motivos?.map((item, idx) => (
                  <MenuItem
                    key={`${item?.descricao}-${idx}`}
                    value={item.descricao}
                  >
                    {item?.descricao}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </td>
          <td colSpan={2}>
            <span className="tableTitle">{`${t("Subreason")}:`}</span>{" "}
            <Box sx={{ minWidth: 200 }}>
              <Select
                value={
                  row?.motivos
                    ?.find((item) => item.descricao === reason)
                    ?.subMotivos.some((item) => item.descricao === subReason)
                    ? subReason
                    : ""
                }
                onChange={({ target }) => {
                  setSubReason(target.value);
                }}
                fullWidth
                size="small"
              >
                <MenuItem key="null" value="">
                  Selecione
                </MenuItem>
                {row?.motivos
                  ?.find((item) => item.descricao === reason)
                  ?.subMotivos.map((item, idx) => (
                    <MenuItem
                      key={`${item?.descricao}${idx}`}
                      value={item.descricao}
                    >
                      {item.descricao}
                    </MenuItem>
                  ))}
              </Select>
            </Box>
          </td>
        </>
      )}
    </tr>
  );
};

export default ReasonSubReasonModalTr;
