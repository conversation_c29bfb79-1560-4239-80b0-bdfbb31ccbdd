import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useDataTableCommons } from "@/hooks/useDataTableCommons";
import { IProcurementApprovalReprovalParams } from "@/interfaces/procurement";
import useOnelogStore from "@/stores/onelogStore";
import {
  FC,
  JSX,
  startTransition,
  useCallback,
  useMemo,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { IOnelog } from "../../interfaces";
import { OneLogService } from "../../services";
import { ActionButtons } from "../ActionButtons";
import { HierarchicalDataTableBase } from "../HierarchicalDataTableBase";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

interface Column<T> {
  id: string;
  label: string;
  accessor: keyof T | ((item: T) => string | number | JSX.Element);
  width?: string;
  align?: "left" | "center" | "right";
}

interface DataTableOneLogHierarchicalProps {
  headerData: IOnelog.ClientProps[];
  detailData?: IOnelog.ClientProps[];
  process: string;
}

export const DataTableOneLogHierarchical: FC<
  DataTableOneLogHierarchicalProps
> = ({ headerData, detailData, process }) => {
  const {
    headerColumns,
    detailColumns,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    approveItems,
    origin,
  } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const { reason, setReason, subReason, setSubReason } = useOnelogStore();

  const { modalState, handleOpenModal, handleCloseModal } =
    useDataTableCommons<IOnelog.ItemDetailProps>();

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: IOnelog.AprovacoesProps | null;
    isApproveFromDetailModal: boolean;
  }>({
    open: false,
    type: null,
    row: null,
    isApproveFromDetailModal: false,
  });

  const { isLoading: isLoadingApprovalReprovalFromModal, mutate } = useMutation(
    async ({
      status,
      id,
      reasonReproval,
    }: {
      status: "A" | "R";
      id: string;
      reasonReproval: string;
    }) => {
      const data = await OneLogService.approvalReprovalDetailDocument({
        status,
        id,
        motivo: reason ?? "",
        submotivo: subReason ?? "",
        employeeID: user.employeeID,
        reason: reasonReproval,
      });

      return data;
    },
    {
      onSuccess(data) {
        toast.success(t(data.message ?? ""));
      },
    }
  );

  const handleModalApprove = useCallback(
    async (data: IOnelog.AprovacoesProps[]) => {
      const row = Array.isArray(data) ? data[0] : data;
      handleApprovalReprovalDocument(row, "A", true);
      handleCloseModal();
    },
    []
  );

  const handleModalReject = useCallback((data: IOnelog.AprovacoesProps[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenApprovalModal(row, "disapprove", true);
    handleCloseModal();
  }, []);

  const handleOpenApprovalModal = useCallback(
    (
      row: IOnelog.AprovacoesProps,
      type: "approve" | "disapprove",
      isApproveFromDetailModal: boolean = false
    ) => {
      setApprovalModalState({
        open: true,
        type,
        row,
        isApproveFromDetailModal,
      });
    },
    []
  );

  const handleCloseReprovalModal = useCallback(() => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
      isApproveFromDetailModal: false,
    });
  }, []);

  const handleConfirmReprovalModal = useCallback(
    async (reason: string) => {
      if (approvalModalState.row && approvalModalState.type) {
        await handleApprovalReprovalDocument(
          approvalModalState.row,
          "R",
          approvalModalState.isApproveFromDetailModal,
          reason
        );
        handleCloseReprovalModal();
      }
    },
    [
      approvalModalState.row,
      approvalModalState.type,
      approvalModalState.isApproveFromDetailModal,
    ]
  );

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<IProcurementApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: IOnelog.AprovacoesProps,
      status: "A" | "R",
      isApproveFromDetailModal: boolean = false,
      reasonReproval: string = ""
    ) => {
      if (isApproveFromDetailModal) {
        mutate({
          status,
          id: row.id,
          reasonReproval: reasonReproval,
        });
      } else {
        approveReproveDocument({
          rowData: row,
          origin: origin as ApprovalOrigin,
          process,
          status: status,
          reason: reasonReproval,
        });
      }
    },
    [process, origin, approveReproveDocument, mutate]
  );

  const handleDataToModal = useCallback(
    (row: IOnelog.AprovacoesProps) => {
      const documentNumber = row.id;

      startTransition(() => {
        OneLogService.getDocumentDetails(documentNumber)
          .then((data) => {
            if (data) {
              handleOpenModal(data);
              setReason(data?.motivo || data?.motivos[0].descricao);
              setSubReason(data?.subMotivo);
            }
          })
          .catch((error) => console.error(error));
      });
    },
    [setReason, setSubReason, handleOpenModal]
  );

  const transformedParentColumns = useMemo(
    (): Column<IOnelog.ClientProps>[] =>
      headerColumns?.map((col) => ({
        id: String(col.id || col.name),
        label: col.name as string,
        accessor: col.selector
          ? (item: IOnelog.ClientProps) => {
              const value = col.selector!(item);
              return typeof value === "object" ? String(value) : String(value);
            }
          : ("nome" as keyof IOnelog.ClientProps),
        width: col.width,
        align: "left" as const,
      })) || [],
    [headerColumns]
  );

  const transformedChildColumns = useMemo(
    (): Column<IOnelog.AprovacoesProps>[] =>
      detailColumns?.map((col) => ({
        id: String(col.id || col.name),
        label: col.name as string,
        accessor: col.selector
          ? (item: IOnelog.AprovacoesProps) => {
              const value = col.selector!(item);
              return typeof value === "object" ? String(value) : String(value);
            }
          : ("id" as keyof IOnelog.AprovacoesProps),
        width: col.width,
        align: "left" as const,
      })) || [],
    [detailColumns]
  );

  const allChildData = useMemo(
    (): IOnelog.AprovacoesProps[] =>
      detailData?.flatMap((detail) => detail.aprovacoes || []) || [],
    [detailData]
  );

  const renderChildActions = useCallback(
    (child: IOnelog.AprovacoesProps) => {
      if (user.onlyReading) return <></>;
      return (
        <ActionButtons<IOnelog.AprovacoesProps>
          row={child}
          onApprove={async () => {
            handleApprovalReprovalDocument(child, "A");
          }}
          onDisapprove={() => {
            handleOpenApprovalModal(child, "disapprove");
          }}
          showOpenModalButton={hasDetailModal}
          onOpenModal={handleDataToModal}
          showOpenUrlButton={false}
        />
      );
    },
    [
      user.onlyReading,
      hasDetailModal,
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
    ]
  );

  const handleSelectionChange = useCallback(() => {
    // TODO: Implementar lógica de seleção conforme necessário
    // Exemplo: atualizar estado global, enviar para API, etc.
  }, []);

  const getChildParentId = useCallback(
    (child: IOnelog.AprovacoesProps): string => {
      const parentData = detailData?.find((detail) =>
        detail.aprovacoes?.some((approval) => approval.id === child.id)
      );
      return parentData?.nome || "";
    },
    [detailData]
  );

  return (
    <div>
      <HierarchicalDataTableBase<IOnelog.ClientProps, IOnelog.AprovacoesProps>
        parentData={headerData}
        childData={allChildData}
        parentColumns={transformedParentColumns}
        childColumns={transformedChildColumns}
        getParentId={(parent) => parent.nome}
        getChildId={(child) => child.id}
        getChildParentId={getChildParentId}
        actionPosition="child"
        expandMode="actions"
        renderChildActions={approveItems ? renderChildActions : undefined}
        onSelectionChange={handleSelectionChange}
        enableSelection={true}
        enableSelectionChildren={true}
        enableSelectAll={true}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseReprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmReprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor={
          approvalModalState.type === "approve" ? "success" : "error"
        }
        isRequired={approvalModalState.type === "disapprove"}
      />

      <Loading open={isLoadingApproval || isLoadingApprovalReprovalFromModal} />
    </div>
  );
};
