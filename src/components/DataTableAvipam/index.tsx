import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";

import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { AvipamItem } from "@/interfaces/avipam";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableAvipam: FC<{
  headerData: AvipamItem[];
  process: string;
}> = ({ headerData, process }) => {
  const {
    headerColumns,
    origin,
    title,
    detailModalHeader,
    detailModalContent,
  } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: AvipamItem | AvipamItem[];
  }>({ open: false, data: [] as AvipamItem[] });

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: AvipamItem | null;
  }>({
    open: false,
    type: null,
    row: null,
  });

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<any>();

  const handleApprovalReprovalDocument = useCallback(
    async (row: AvipamItem, status: "A" | "R", reason?: string) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [process, origin]
  );

  const handleOpenModal = (data: AvipamItem | AvipamItem[]) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as AvipamItem[] });
  };

  const handleModalApprove = async (data: AvipamItem | AvipamItem[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    await handleApprovalReprovalDocument(row, "A");
    handleCloseModal();
  };

  const handleModalReject = (data: AvipamItem | AvipamItem[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenApprovalModal(row, "disapprove");
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: AvipamItem,
    type: "approve" | "disapprove"
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
    });
  };

  const handleCloseApprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (reason: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status = approvalModalState.type === "approve" ? "A" : "R";

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        reason
      );
      handleCloseApprovalModal();
    }
  };

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (columns: TableColumn<AvipamItem>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column?.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: AvipamItem) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<AvipamItem>
                    row={row}
                    onApprove={() => {
                      handleApprovalReprovalDocument(row, "A");
                    }}
                    onDisapprove={() =>
                      handleOpenApprovalModal(row, "disapprove")
                    }
                    showOpenUrlButton={false}
                    showOpenModalButton={!!detailModalHeader}
                    onOpenModal={() => handleOpenModal(row)}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleOpenApprovalModal,
      handleApprovalReprovalDocument,
      user.onlyReading,
      handleOpenModal,
      detailModalHeader,
    ]
  );

  addActionColumn(headerColumns as TableColumn<AvipamItem>[]);

  return (
    <div className={`Avipam-data`}>
      <MainDataTable
        rowKey={(row: AvipamItem) => row.orderNumber}
        columns={headerColumns as TableColumn<AvipamItem>[]}
        data={headerData}
      />
      {modalState.open && modalState.data && detailModalHeader && (
        <ModalItemDetails
          open={modalState.open}
          onClose={handleCloseModal}
          modalTitle={title}
          data={modalState.data}
          detailModalHeader={detailModalHeader}
          detailModalContent={detailModalContent}
          origin={origin}
          onApprove={handleModalApprove}
          onReject={handleModalReject}
        />
      )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor={"error"}
        isRequired={approvalModalState.type === "disapprove"}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
