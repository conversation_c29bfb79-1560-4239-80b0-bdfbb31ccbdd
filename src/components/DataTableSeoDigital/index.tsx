import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ISeoDigital } from "../../interfaces";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";

export const DataTableSeoDigital: FC<{
  headerData: ISeoDigital.ISeoDigitalBase[];
  process: string;
}> = ({ headerData, process }) => {
  const { headerColumns, origin } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: ISeoDigital.ISeoDigitalBase | null;
  }>({
    open: false,
    row: null,
  });

  const handleOpenDisapprovalModal = (row: ISeoDigital.ISeoDigitalBase) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (comment: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "R",
        comment
      );
      handleCloseDisapprovalModal();
    }
  };

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<ISeoDigital.ApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: ISeoDigital.ISeoDigitalBase,
      status: "A" | "R",
      reason?: string
    ) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [process, origin]
  );

  const addActionColumn = useCallback(
    (columns: TableColumn<ISeoDigital.ISeoDigitalBase>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "185px",
          maxWidth: "185px !important",
          cell: (row: ISeoDigital.ISeoDigitalBase) => {
            const linkToPortal = `${
              import.meta.env.VITE_PORTAL_AUTO_CONTROLE_MANU
            }/${row.topicId}/${row.id}`;

            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<ISeoDigital.ISeoDigitalBase>
                    row={row}
                    onApprove={() => handleApprovalReprovalDocument(row, "A")}
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    showOpenUrlButton={true}
                    showOpenModalButton={false}
                    openUrl={linkToPortal}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [handleApprovalReprovalDocument, user.onlyReading]
  );

  addActionColumn(headerColumns as TableColumn<ISeoDigital.ISeoDigitalBase>[]);

  return (
    <div className="SeoDigitial-data-table-container">
      <MainDataTable
        rowKey={(row: ISeoDigital.ISeoDigitalBase) => row.id}
        columns={headerColumns as TableColumn<ISeoDigital.ISeoDigitalBase>[]}
        data={headerData}
      />

      <ModalApprovalReason
        open={disapprovalModalState.open}
        onClose={handleCloseDisapprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmDisapprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
