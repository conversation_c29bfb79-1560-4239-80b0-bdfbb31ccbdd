import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import dayjs from "dayjs";

import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { DatePicker as DatePickerMUI } from "@mui/x-date-pickers";

import { DelegationAPI } from "@/api/delegation";
import i18n from "@/hooks/translation";
import { ISearchUser } from "@/interfaces";
import { useCards } from "@/stores/cardsStore";
import { t } from "i18next";
import { MouseEvent, useState } from "react";
import { useMutation, useQueryClient } from "react-query";
import { toast } from "react-toastify";
import { useAuth, useSearchUsers } from "../../hooks";
import { Select } from "./components/Select";

export const ModalNewDelegation = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (value: boolean) => void;
}) => {
  const qasEnv = import.meta.env.VITE_ENVIROMENT;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { getTokenForThisUser, login, user } = useAuth();
  const { setCards } = useCards(user);
  const {
    options,
    isLoadingOptions,
    handleInputChange,
    loadMore,
    setLoadMore,
    resetValues,
    loadMoreResults,
  } = useSearchUsers(getTokenForThisUser, login, setCards, "reports");

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Datas selecionadas
  const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(null);
  const [endDate, setEndDate] = useState<dayjs.Dayjs | null>(null);
  const [selectedValue, setSelectedValue] = useState<
    ISearchUser.SearchUsersAccounts[]
  >([]);
  const queryClient = useQueryClient();
  const { mutate: createDelegation, isLoading: isSaving } = useMutation(
    async () => {
      if (!selectedValue || !startDate || !endDate) {
        throw new Error("Missing required fields");
      }
      const params = {
        delegatorInitials: user.initials,
        userInitials: selectedValue[0].initials,
        startDate: startDate.format("YYYY-MM-DD"),
        endDate: endDate.format("YYYY-MM-DD"),
        language: i18n.language.toUpperCase(),
        employeeid: user.employeeID,
      };

      return await DelegationAPI.recordDelegation(params);
    },
    {
      onSuccess: (res) => {
        toast.success(res.data);
        setOpen(false);
        queryClient.invalidateQueries([
          "delegations",
          "byMe",
          user?.employeeID,
        ]);
      },
      onError: (err) => {
        console.error("Failed to save delegation:", err);
      },
    }
  );

  return (
    <>
      <Dialog
        data-testid="modalNewDelegationComponent"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{t("New.Delegation")}</DialogTitle>
        <DialogContent style={{ padding: "2rem" }}>
          <Box
            display="flex"
            alignItems="center"
            onMouseEnter={(e) => {
              e.currentTarget.style.cursor = "pointer";
            }}
            onClick={handleClick}
            aria-controls={open ? "search-users" : undefined}
            aria-haspopup="true"
            aria-expanded={open ? "true" : undefined}
            aria-label="Abrir select de usuários para representar"
          >
            <Select
              value={selectedValue ?? ""}
              options={options}
              onChange={(value) => setSelectedValue(value)}
              onInputChange={handleInputChange}
              isLoadingOptions={isLoadingOptions}
              loadMore={loadMore}
              setLoadMore={setLoadMore}
              loadMoreResults={loadMoreResults}
              resetValues={resetValues}
            />
          </Box>

          <div
            style={{
              marginTop: isMobile ? "1rem" : "2rem",
              justifyContent: "space-between",
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              gap: "1rem",
            }}
          >
            <DatePickerMUI
              data-testid="initialDateComponent"
              label={t("Start.Date.II")}
              value={startDate}
              onChange={(val) => setStartDate(val as dayjs.Dayjs | null)}
              shouldDisableDate={(date) => dayjs(date).isBefore(dayjs(), "day")}
              format="DD/MM/YYYY"
              slotProps={{
                popper: {
                  placement: isMobile ? "bottom" : "auto",
                  modifiers: [
                    {
                      name: "preventOverflow",
                      options: {
                        altAxis: true,
                        tether: true,
                        rootBoundary: "viewport",
                        padding: 8,
                      },
                    },
                  ],
                },
              }}
            />

            <DatePickerMUI
              data-testid="finalDateComponent"
              label={t("End.Date")}
              value={endDate}
              onChange={(val) => setEndDate(val as dayjs.Dayjs | null)}
              minDate={startDate ?? dayjs()}
              shouldDisableDate={(date) => dayjs(date).isBefore(dayjs(), "day")}
              format="DD/MM/YYYY"
              slotProps={{
                popper: {
                  placement: isMobile ? "bottom" : "auto",
                  modifiers: [
                    {
                      name: "preventOverflow",
                      options: {
                        altAxis: true,
                        tether: true,
                        rootBoundary: "viewport",
                        padding: 8,
                      },
                    },
                  ],
                },
              }}
            />
          </div>
        </DialogContent>
        <DialogActions
          style={{
            paddingBottom: "2rem",
            paddingRight: "2rem",
            display: "flex",
            flexDirection: "row",
          }}
        >
          <Button
            variant="outlined"
            color="error"
            sx={{ borderRadius: "30px" }}
            onClick={handleClose}
            autoFocus
          >
            {t("Close")}
          </Button>

          <Button
            variant="contained"
            color="success"
            sx={{ borderRadius: "30px" }}
            onClick={() => createDelegation()}
            // disabled={
            //   isSaving ||
            //   !selectedValue ||
            //   !startDate ||
            //   !endDate ||
            //   (startDate && endDate
            //     ? endDate.isBefore(startDate, "day")
            //     : false)
            // }
            autoFocus
          >
            {isSaving ? t("Saving", "Salvando...") : t("Save")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
