import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { ExpandMore } from "@mui/icons-material";
import {
  Box,
  Collapse,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { JSX, useCallback, useEffect, useMemo, useState } from "react";
import { v4 } from "uuid";
import { useStore } from "zustand";
import { HierarchicalCheckbox } from "./components/HierarchicalCheckbox";
import {
  createHierarchicalSelectionStore,
  HierarchicalSelectionContext,
  useHierarchicalSelectionStore,
} from "./store/hierarchicalSelectionStore";

interface Column<T> {
  id: string;
  label: string;
  accessor: keyof T | ((item: T) => string | number | JSX.Element);
  width?: string;
  align?: "left" | "center" | "right";
}

type ActionPosition = "parent" | "child" | "both" | "none";

interface HierarchicalDataTableBaseProps<TParent, TChild> {
  tableId?: string;
  parentData: TParent[];
  childData?: TChild[];
  parentColumns: Column<TParent>[];
  childColumns: Column<TChild>[];
  childHTML?: (rows: TChild | TChild[]) => React.ReactNode;
  renderDetailRowColor?: (row: TChild) => string;
  getParentId: (parent: TParent) => string;
  getChildId: (child: TChild) => string;
  getChildParentId: (child: TChild) => string;
  actionPosition?: ActionPosition;
  renderParentActions?: (item: TParent) => JSX.Element;
  renderChildActions?: (item: TChild) => JSX.Element;
  expandMode?: "actions" | "view-only";
  onSelectionChange?: (
    selectedParents: TParent[],
    selectedChildren: TChild[]
  ) => void;
  enableSelection?: boolean;
  enableSelectionChildren?: boolean;
  enableSelectAll?: boolean;
}

const HierarchicalTableInternal = <TParent, TChild>({
  parentData,
  childData = [],
  parentColumns,
  childColumns,
  childHTML,
  getParentId,
  getChildId,
  getChildParentId,
  actionPosition = "child",
  renderParentActions,
  renderChildActions,
  onSelectionChange,
  renderDetailRowColor,
  enableSelection = true,
  enableSelectionChildren = false,
  enableSelectAll = true,
}: HierarchicalDataTableBaseProps<TParent, TChild>): JSX.Element => {
  const store = useHierarchicalSelectionStore();
  const {
    setNodes,
    toggleSelection,
    isNodeSelected,
    isNodeIndeterminate,
    getSelectedData,
    changeCounter,
  } = useStore(store);

  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const { addSelectedRows } = useSelectedRowsStore();

  const tableId = useMemo(() => v4(), []);

  const hierarchicalNodes = useMemo(
    () => [
      ...parentData.map((parent) => ({
        id: getParentId(parent),
        data: parent,
      })),
      ...childData.map((child) => ({
        id: getChildId(child),
        parentId: getChildParentId(child),
        data: child,
      })),
    ],
    [parentData, childData, getParentId, getChildId, getChildParentId]
  );

  useEffect(() => {
    const prevNodesObj = store.getState().nodes;

    // Convert prevNodes to array if it's an object
    const prevNodes = Array.isArray(prevNodesObj)
      ? prevNodesObj
      : Object.values(prevNodesObj);

    const isSame =
      Array.isArray(prevNodes) &&
      prevNodes.length === hierarchicalNodes.length &&
      prevNodes.every((node, idx) => node.id === hierarchicalNodes[idx].id);

    if (!isSame) {
      setNodes(hierarchicalNodes);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hierarchicalNodes]);

  const selectedData = useMemo(() => {
    if (!enableSelection) return [];
    return getSelectedData();
  }, [enableSelection, changeCounter, getSelectedData]);

  useEffect(() => {
    if (enableSelection) {
      const selectedParents = selectedData.filter((data: unknown) =>
        parentData.some((parent) => data === parent)
      ) as TParent[];
      const selectedChildren = selectedData.filter((data: unknown) =>
        childData.some((child) => data === child)
      ) as TChild[];

      if (selectedChildren.length > 0) {
        addSelectedRows(tableId, selectedChildren);
      } else {
        addSelectedRows(tableId, selectedParents);
      }
      onSelectionChange?.(selectedParents, selectedChildren);
    }
  }, [
    changeCounter,
    onSelectionChange,
    parentData,
    childData,
    enableSelection,
    selectedData,
  ]);

  const handleToggleExpand = useCallback((parentId: string) => {
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(parentId)) {
        newSet.delete(parentId);
      } else {
        newSet.add(parentId);
      }
      return newSet;
    });
  }, []);

  const getChildrenForParent = useCallback(
    (parentId: string) => {
      return childData.filter((child) => getChildParentId(child) === parentId);
    },
    [childData, getChildParentId]
  );

  const renderCellContent = useCallback(
    <T,>(item: T, column: Column<T>): string | number | JSX.Element => {
      if (typeof column.accessor === "function") {
        return column.accessor(item);
      }
      const value = item[column.accessor];
      return String(value ?? "");
    },
    []
  );

  const showParentActions =
    actionPosition === "parent" || actionPosition === "both";
  const showChildActions =
    actionPosition === "child" || actionPosition === "both";
  const hasExpandableContent = childData.length > 0;

  const allParentsSelected = useMemo(
    () => parentData.every((parent) => isNodeSelected(getParentId(parent))),
    [parentData, getParentId, isNodeSelected, changeCounter]
  );

  const someParentsSelected = useMemo(
    () =>
      parentData.some(
        (parent) =>
          isNodeSelected(getParentId(parent)) ||
          isNodeIndeterminate(getParentId(parent))
      ),
    [
      parentData,
      getParentId,
      isNodeSelected,
      isNodeIndeterminate,
      changeCounter,
    ]
  );

  const renderParentRow = useCallback(
    (parent: TParent) => {
      const parentId = getParentId(parent);
      const children =
        childColumns.length > 0 ? getChildrenForParent(parentId) : [];
      const hasChildren = children.length > 0;
      const isExpanded = expandedRows.has(parentId);
      const isSelected = enableSelection ? isNodeSelected(parentId) : false;
      const isIndeterminate = enableSelection
        ? isNodeIndeterminate(parentId)
        : false;

      return (
        <>
          <TableRow
            key={parentId}
            hover
            sx={{
              backgroundColor: isExpanded ? "rgb(245, 245, 245)" : undefined,
              "&:hover": {
                backgroundColor: isExpanded ? "rgb(245, 245, 245)" : "#f5f5f5",
              },
              cursor: "pointer",
            }}
          >
            {enableSelection && (
              <TableCell padding="checkbox">
                <HierarchicalCheckbox
                  checked={isSelected}
                  indeterminate={isIndeterminate}
                  onChange={() => toggleSelection(parentId)}
                />
              </TableCell>
            )}

            <TableCell>
              {hasChildren && hasExpandableContent && (
                <IconButton
                  size="small"
                  onClick={() => handleToggleExpand(parentId)}
                  sx={{
                    transform: isExpanded ? "rotate(0deg)" : "rotate(-90deg)",
                    transition: "transform 0.2s",
                    color: "#812990",
                    "&:hover": {
                      backgroundColor: "rgba(129, 41, 144, 0.04)",
                      transform: isExpanded
                        ? "rotate(0deg) scale(1.1)"
                        : "rotate(-90deg) scale(1.1)",
                    },
                  }}
                >
                  <ExpandMore sx={{ fontSize: 20 }} />
                </IconButton>
              )}
            </TableCell>

            {parentColumns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || "left"}
                style={{ width: column.width }}
                sx={{
                  fontSize: "14px",
                  fontFamily: "Roboto, sans-serif",
                  padding: "8px",
                }}
              >
                {renderCellContent(parent, column)}
              </TableCell>
            ))}

            {showParentActions && renderParentActions && (
              <TableCell align="center">
                {renderParentActions(parent)}
              </TableCell>
            )}
          </TableRow>

          {hasChildren && hasExpandableContent && (
            <TableRow>
              <TableCell
                colSpan={
                  parentColumns.length +
                  (enableSelection ? 1 : 0) +
                  1 +
                  (showParentActions ? 1 : 0)
                }
                style={{
                  paddingBottom: 0,
                  paddingTop: 0,
                  backgroundColor: "rgb(245, 245, 245)",
                }}
              >
                <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                  <Box margin={1}>
                    {!!childHTML &&
                      typeof childHTML === "function" &&
                      childHTML(children)}

                    {childColumns.length > 0 && (
                      <Table size="small">
                        <TableHead>
                          <TableRow
                            sx={{ backgroundColor: "rgb(245, 245, 245)" }}
                          >
                            {enableSelectionChildren && (
                              <TableCell
                                padding="checkbox"
                                sx={{
                                  backgroundColor: "rgb(245, 245, 245)",
                                  fontSize: "14px",
                                  fontWeight: "bold",
                                  fontFamily: "Roboto, sans-serif",
                                }}
                              />
                            )}
                            {childColumns.map((column) => (
                              <TableCell
                                key={column.id}
                                align={column.align || "left"}
                                style={{ width: column.width }}
                                sx={{
                                  backgroundColor: "rgb(245, 245, 245)",
                                  fontSize: "14px",
                                  fontWeight: "bold",
                                  fontFamily: "Roboto, sans-serif",
                                }}
                              >
                                <Typography
                                  variant="subtitle2"
                                  fontWeight="bold"
                                  sx={{
                                    fontSize: "14px",
                                    fontFamily: "Roboto, sans-serif",
                                  }}
                                >
                                  {column.label
                                    ? t(column.label)
                                    : column.label}
                                </Typography>
                              </TableCell>
                            ))}
                            {showChildActions && (
                              <TableCell
                                align="center"
                                sx={{
                                  backgroundColor: "rgb(245, 245, 245)",
                                  fontSize: "14px",
                                  fontWeight: "bold",
                                  fontFamily: "Roboto, sans-serif",
                                }}
                              >
                                {t("Actions")}
                              </TableCell>
                            )}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {children.map((child) => renderChildRow(child))}
                        </TableBody>
                      </Table>
                    )}
                  </Box>
                </Collapse>
              </TableCell>
            </TableRow>
          )}
        </>
      );
    },
    [
      getParentId,
      getChildrenForParent,
      expandedRows,
      isNodeSelected,
      isNodeIndeterminate,
      toggleSelection,
      handleToggleExpand,
      parentColumns,
      renderCellContent,
      showParentActions,
      renderParentActions,
      enableSelection,
      hasExpandableContent,
      enableSelectionChildren,
    ]
  );

  const renderChildRow = useCallback(
    (child: TChild) => {
      const childId = getChildId(child);
      const isSelected = enableSelectionChildren
        ? isNodeSelected(childId)
        : false;
      const isIndeterminate = enableSelectionChildren
        ? isNodeIndeterminate(childId)
        : false;

      return (
        <TableRow
          key={childId}
          hover
          sx={{
            backgroundColor: !!renderDetailRowColor
              ? renderDetailRowColor(child)
              : "rgb(245, 245, 245)",
            "&:hover": {
              backgroundColor: !!renderDetailRowColor
                ? `${renderDetailRowColor(child)} !important`
                : "rgb(245, 245, 245)",
              opacity: 0.8,
            },
            cursor: "pointer",
          }}
        >
          {enableSelectionChildren && (
            <TableCell padding="checkbox">
              <HierarchicalCheckbox
                checked={isSelected}
                indeterminate={isIndeterminate}
                onChange={() => toggleSelection(childId)}
              />
            </TableCell>
          )}

          {childColumns.map((column) => (
            <TableCell
              key={column.id}
              align={column.align || "left"}
              style={{ width: column.width }}
              sx={{
                fontSize: "14px",
                fontFamily: "Roboto, sans-serif",
                padding: "8px",
              }}
            >
              {renderCellContent(child, column)}
            </TableCell>
          ))}

          {showChildActions && renderChildActions && (
            <TableCell align="center">{renderChildActions(child)}</TableCell>
          )}
        </TableRow>
      );
    },
    [
      getChildId,
      isNodeSelected,
      isNodeIndeterminate,
      toggleSelection,
      childColumns,
      renderCellContent,
      showChildActions,
      renderChildActions,
      enableSelection,
      enableSelectionChildren,
      renderDetailRowColor,
    ]
  );

  const handleSelectAllParents = useCallback(
    (checked: boolean) => {
      if (checked) {
        parentData.forEach((parent) => {
          const parentId = getParentId(parent);
          if (!isNodeSelected(parentId)) {
            toggleSelection(parentId);
          }
        });
      } else {
        parentData.forEach((parent) => {
          const parentId = getParentId(parent);
          if (isNodeSelected(parentId)) {
            toggleSelection(parentId);
          }
        });
      }
    },
    [parentData, getParentId, isNodeSelected, toggleSelection]
  );

  return (
    <TableContainer component={Paper} elevation={1}>
      <Table data-table-id={tableId}>
        <TableHead>
          <TableRow>
            {enableSelection && (
              <TableCell padding="checkbox">
                {enableSelectAll && (
                  <HierarchicalCheckbox
                    checked={allParentsSelected}
                    indeterminate={!allParentsSelected && someParentsSelected}
                    onChange={(checked) => {
                      handleSelectAllParents(checked);
                    }}
                  />
                )}
              </TableCell>
            )}

            <TableCell />

            {parentColumns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || "left"}
                style={{ width: column.width }}
                sx={{
                  fontSize: "14px",
                  fontWeight: "bold",
                  fontFamily: "Roboto, sans-serif",
                }}
              >
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    fontSize: "14px",
                    fontFamily: "Roboto, sans-serif",
                  }}
                >
                  {column.label ? t(column.label) : column.label}
                </Typography>
              </TableCell>
            ))}

            {showParentActions && (
              <TableCell align="center">
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    fontSize: "14px",
                    fontFamily: "Roboto, sans-serif",
                  }}
                >
                  Ações
                </Typography>
              </TableCell>
            )}
          </TableRow>
        </TableHead>

        <TableBody>
          {parentData.map((parent) => renderParentRow(parent))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export const HierarchicalDataTableBase = <TParent, TChild>(
  props: HierarchicalDataTableBaseProps<TParent, TChild>
): JSX.Element => {
  const store = useMemo(() => createHierarchicalSelectionStore(), []);

  return (
    <HierarchicalSelectionContext.Provider value={store}>
      <HierarchicalTableInternal {...props} />
    </HierarchicalSelectionContext.Provider>
  );
};
