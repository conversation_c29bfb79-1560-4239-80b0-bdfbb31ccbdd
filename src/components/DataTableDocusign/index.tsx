import { IDocusign } from "@/interfaces";
import { DocuSignService } from "@/services/docuSign";
import { FC, useCallback, useState, useTransition } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { MainDataTable, SecondaryDataTable } from "..";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ActionButtons } from "../ActionButtons";
import Loading from "../Loading";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableDocuSign: FC<{
  headerData: IDocusign.ItemProps[] | any;
  detailData?: IDocusign.ItemProps[] | any;
  process: string;
}> = ({ headerData, detailData, process }) => {
  const {
    headerColumns,
    detailColumns,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    hasDetailRoute,
    approveItems,
    origin,
    additional1,
    additional2,
  } = processesProps(process)[0] || {};

  const { user } = useAuth();
  // const { execute, loadingDataTable, updateCounter } = useCards(user);
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IDocusign.ItemProps | IDocusign.ItemProps[];
  }>({ open: false, data: [] as IDocusign.ItemProps[] });
  const [expandedRows, setExpandedRows] = useState<IDocusign.ItemProps[]>([]);

  const handleOpenModal = (data: any) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as IDocusign.ItemProps[] });
  };

  const handleDataToModal = useCallback(
    (row: IDocusign.ItemProps) => {
      const additional1Value = additional1 ? additional1(row) : "";
      const additional2Value = additional2 ? additional2(row) : "";

      startTransition(async () => {
        const { data } = await DocuSignService.getDocumentDetails(
          additional1Value,
          additional2Value
        );

        if (!data.length) return;

        handleOpenModal(data);
      });
    },
    [hasDetailRoute, process, headerData]
  );

  const addActionColumn = useCallback(
    (columns: TableColumn<IDocusign.ItemProps>[], hasDetailModal: boolean) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: IDocusign.ItemProps) =>
            !user.onlyReading ? (
              <div>
                <ActionButtons<IDocusign.ItemProps>
                  row={row}
                  showOpenModalButton={hasDetailModal}
                  onOpenModal={() => handleDataToModal(row)}
                  // openUrl={() => handleDataToModal(row)}
                  showApproveButton={false}
                  showDisapproveButton={false}
                />
              </div>
            ) : null,
        });
      }
    },
    [handleDataToModal, user.onlyReading, process]
  );

  addActionColumn(
    headerColumns as TableColumn<IDocusign.ItemProps>[],
    !!hasDetailModal
  );

  const renderExpandableRowsComponent = useCallback(
    ({ data }: { data: IDocusign.ItemProps }) => {
      const row = data as IDocusign.ItemProps;
      if (!detailData?.length || !detailColumns) return null;

      const documentDetails = row.SIGNERS;
      return (
        <SecondaryDataTable
          columns={detailColumns as TableColumn<IDocusign.SignersProps>[]}
          data={documentDetails ?? []}
        />
      );
    },
    [detailData, detailColumns, approveItems]
  );

  return (
    <div>
      <MainDataTable
        expandedRows={expandedRows}
        rowKey={(row: IDocusign.ItemProps) => row.DOCUMENTO}
        columns={headerColumns as TableColumn<IDocusign.ItemProps>[]}
        data={headerData}
        expandableRows={!!detailData?.length}
        selectableRows={false}
        onRowExpandToggled={(expanded: boolean, row: IDocusign.ItemProps) => {
          if (expanded) {
            setExpandedRows((prev) => [...prev, row]);
          } else {
            setExpandedRows((prev) =>
              prev.filter(
                (expandedRow) => expandedRow.DOCUMENTO !== row.DOCUMENTO
              )
            );
          }
        }}
        expandableRowsComponent={({ data }: { data: IDocusign.ItemProps }) => {
          return detailColumns ? renderExpandableRowsComponent({ data }) : null;
        }}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            detailModalContent={detailModalContent}
            origin={origin}
          />
        )}

      <Loading open={isPending} />
    </div>
  );
};
