import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { IIntranet } from "@/interfaces";
import { AttachFileOutlined } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import { FC, useCallback, useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableIntranet: FC<{
  headerData: IIntranet.IntranetProps[] | any;
  detailData?: IIntranet.IntranetProps[] | any;
  type: string;
}> = ({ headerData, detailData, type }) => {
  const {
    headerColumns,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    origin,
  } = processesProps(type)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    setTableData(headerData);
  }, [headerData]);

  const [tableData, setTableData] =
    useState<IIntranet.IntranetProps[]>(headerData);
  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IIntranet.IntranetProps | IIntranet.IntranetProps[];
  }>({ open: false, data: [] as IIntranet.IntranetProps[] });

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: IIntranet.IntranetProps | null;
  }>({
    open: false,
    type: null,
    row: null,
  });

  const handleOpenModal = (
    data: IIntranet.IntranetProps | IIntranet.IntranetProps[]
  ) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as IIntranet.IntranetProps[] });
  };

  const handleModalApprove = async (data: IIntranet.IntranetProps) => {
    await handleApprovalReprovalDocument(data, "A");
    handleCloseModal();
  };

  const handleModalReject = (data: IIntranet.IntranetProps) => {
    handleOpenApprovalModal(data, "disapprove");
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: IIntranet.IntranetProps,
    type: "approve" | "disapprove"
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
    });
  };

  const handleCloseApprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (comment: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status = approvalModalState.type === "approve" ? "A" : "R";

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        comment
      );
      handleCloseApprovalModal();
    }
  };

  const { approveReproveDocument, isLoadingApproval } =
    useApprovalDocument<IIntranet.ApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: IIntranet.IntranetProps,
      status: "A" | "R",
      comment?: string
    ) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process: type,
        status: status,
        reason: comment,
      });
    },
    [type, origin]
  );

  const handleDataToModal = useCallback((row: IIntranet.IntranetProps) => {
    handleOpenModal(row);
  }, []);

  const handleOpenUrl = (row: IIntranet.IntranetProps): string => {
    switch (type) {
      case "NR":
        return `${
          import.meta.env.VITE_INTRANET_NORM_URL_BASE
        }modulos/normas/formulario.cfm?codigo=${
          row.CODIGO
        }&status=9&statusrev=5`;

      case "NO":
        return `${
          import.meta.env.VITE_INTRANET_NORM_URL_BASE
        }modulos/normas/formulario.cfm?codigo=${row.CODIGO}&status=4`;
      default:
        return "aaaaaaaaaa";
    }
  };

  const addActionColumn = useCallback(
    (
      columns: TableColumn<IIntranet.IntranetProps>[],
      hasDetailModal: boolean
    ) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "auto",
          minWidth: "100px",
          maxWidth: "300px",
          cell: (row: IIntranet.IntranetProps) => {
            return (
              !user.onlyReading && (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                    padding: "2px",
                  }}
                >
                  <ActionButtons<IIntranet.IntranetProps>
                    row={row}
                    onApprove={async () => {
                      handleApprovalReprovalDocument(row, "A");
                    }}
                    onDisapprove={() => {
                      handleOpenApprovalModal(row, "disapprove");
                    }}
                    showOpenModalButton={hasDetailModal}
                    onOpenModal={() => handleDataToModal(row)}
                    showOpenUrlButton={["NR", "NO"].includes(type)}
                    openUrl={handleOpenUrl(row)}
                  />
                  {["NR", "NO"].includes(type) && row.POSSUI_ANEXO && (
                    <IconButton
                      href={`${
                        import.meta.env.VITE_INTRANET_NORM_URL_BASE
                      }modulos/normas/_anexos.cfm?codigo=${row.CODIGO}`}
                      target="_blank"
                      title={t("Attachments")}
                      size="small"
                      sx={{ padding: "4px" }}
                    >
                      <AttachFileOutlined fontSize="small" />
                    </IconButton>
                  )}
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      user.onlyReading,
      type,
    ]
  );

  addActionColumn(
    headerColumns as TableColumn<IIntranet.IntranetProps>[],
    !!hasDetailModal
  );

  return (
    <div>
      <MainDataTable
        rowKey={(row: IIntranet.IntranetProps) => row.CODIGO}
        columns={headerColumns as TableColumn<IIntranet.IntranetProps>[]}
        data={tableData}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            detailModalContent={detailModalContent}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor={"error"}
        isRequired={approvalModalState.type === "disapprove"}
      />

      <Loading open={isLoadingApproval} />
    </div>
  );
};
