import { api } from "@/api/api";
import { autoDownloadExportApprovals } from "@/utils/autoDownloadExportApprovals";

const baseUrl = import.meta.env.VITE_ANALYTICS

interface GetApprovalsHistoryParams {
  initialDate?: string;
  finalDate?: string;
  process?: string;
  finalStatus?: string;
  page?: number;
  pageSize?: number;
}

interface ApprovalHistoryItem {
  id: number;
  document: string;
  approvalDate: string;
  process: string;
  finalStatus: string;
}

export interface ApprovalHistoryResponse {
  totalItems: number;
  page: number;
  pageSize: number;
  items: ApprovalHistoryItem[];
}

export type ExportApprovalsHistoryFormat = 'xlsx' | 'csv' | 'pdf'

export interface ExportApprovalsHistoryParams {
  initialDate: string;
  finalDate: string;
  process?: string;
  finalStatus?: string;
  exportFormat: ExportApprovalsHistoryFormat;
}

export const ApprovalsHistoryService = {
  async getApprovalsHistory({ initialDate, finalDate, process, finalStatus, page, pageSize }: GetApprovalsHistoryParams): Promise<ApprovalHistoryResponse> {

    const params = new URLSearchParams();

    params.append('initialDate', initialDate ?? '');
    params.append('finalDate', finalDate ?? '');
    params.append('process', process ?? '');
    params.append('finalStatus', finalStatus ?? '');
    params.append('page', String(page));
    params.append('pageSize', String(pageSize));

    const response = await api.get<ApprovalHistoryResponse>(`${baseUrl}/ApprovalHistory?${params}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("employeeToken")}`,
      }
    })

    return response.data
  },

  async exportApprovalsHistory({ initialDate, finalDate, process, finalStatus, exportFormat }: ExportApprovalsHistoryParams): Promise<void> {
    const params = new URLSearchParams();

    params.append('initialDate', initialDate);
    params.append('finalDate', finalDate);
    params.append('process', process ?? '');
    params.append('finalStatus', finalStatus ?? '');
    params.append('exportFormat', exportFormat ?? 'xlsx');

    const response = await api.get(`${baseUrl}/ApprovalHistoryExport?${params}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("employeeToken")}`,
      },
      responseType: 'blob',
    });

    autoDownloadExportApprovals({ data: response.data, exportFormat });

  }
}
