import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { IIntranet } from "@/interfaces";
import { getUniqueDocs } from "@/utils/UniqueDocs";
import { t } from "i18next";
import * as APIIntranet from "../api/intranet";
import { handleApiError } from "./error";

export const IntranetService = {
  /**
   * Prepara os dados para a tabela de aprovações
   */
  prepareDataTable(data: IIntranet.IntranetProps[]): IIntranet.IntranetProps[] {
    if (!data.length) return [];
    if (!data) return data;
    return getUniqueDocs(data).map((doc) => {
      const foundItem = data?.find(
        (resp: IIntranet.IntranetProps) => resp.CODIGO === doc
      );
      return {
        ...foundItem,
      } as IIntranet.IntranetProps;
    });
  },

  /**
   * Busca documentos pendentes da Intranet
   */
  async fetchAndPreparePendingApprovals(
    processType: IIntranet.ProcessKey,
    cache: boolean = true
  ): Promise<IApprovalResponse<IIntranet.IntranetProps>> {
    try {
      const { data, success } = await APIIntranet.getPendingApprovals(
        processType,
        cache
      );

      const prepareDataTable = this.prepareDataTable(data).map((item) => ({
        ...item,
        origin: "INTRANET",
        type: processType,
      }));

      return {
        data: { header: prepareDataTable, detail: [] },
        success,
        message: "",
      };
    } catch (error) {
      handleApiError(error, t, `Intranet ${processType || ""}`.trim());
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },

  async updateDataAfterApproval(process: string): Promise<{
    updatedHeaderData: IIntranet.IntranetProps[];
  }> {
    try {
      const { data } = await this.fetchAndPreparePendingApprovals(
        process as IIntranet.ProcessKey,
        true
      );

      if (!data) {
        return {
          updatedHeaderData: [],
        };
      }

      return {
        updatedHeaderData: data.header,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },

  /**
   * Executa a aprovação/reprovação de um documento
   * @param params - Parâmetros da aprovação/reprovação
   * @param process - Tipo do processo
   */
  approvalReprovalDocument: async (
    params: {
      docId: string;
      status: "A" | "R";
      comments?: string;
    },
    process: IIntranet.ProcessKey
  ): Promise<ApprovalResponse<IIntranet.IntranetProps[]>> => {
    try {
      const response = await APIIntranet.approvalReprovalDocument(
        {
          docId: params.docId,
          status: params.status,
          comments: params.comments,
        },
        process
      );

      if (response.success) {
        const { updatedHeaderData } =
          await IntranetService.updateDataAfterApproval(process);

        return {
          success: response.success,
          data: { header: updatedHeaderData },
        };
      }

      return {
        success: false,
        data: { header: [] },
      };
    } catch (error) {
      handleApiError(error, t, `Intranet ${process || ""}`.trim());
      throw error;
    }
  },
} as const;
