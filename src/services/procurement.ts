import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { IResponse } from "@/interfaces/IResponse";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import { api } from "../api/api";
import * as routes from "../api/routes/procurement";
import {
  IProcurement,
  IProcurementApprovalReprovalParams,
  ItemPropsDetails,
} from "../interfaces/procurement";
import { handleApiError } from "./error";

export const ProcurementService = {
  async getPendingApprovals(
    processType: string,
    cache: boolean = true
  ): Promise<IApprovalResponse<IProcurement>> {
    try {
      const { data, status } = await api.get(
        routes.getPendingApprovals(processType),
        {
          params: { cache },
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );

      const preparedData = data.map((item: IProcurement) => ({
        ...item,
        origin: "PROCUREMENT",
        type: processType,
      }));

      return {
        data: { header: preparedData, detail: [] },
        success: status === 200,
        message: "",
      };
    } catch (error) {
      handleApiError(error, t, `PROCUREMENT ${processType || ""}`.trim());
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },

  async getCircularizationDetail(
    documentId: number
  ): Promise<IResponse<ItemPropsDetails[]>> {
    try {
      const { data, status } = await api.get<ItemPropsDetails[]>(
        routes.getCircularizationDetail(documentId)
      );
      return { data, success: status === 200 };
    } catch (error) {
      handleApiError(error, t, `PROCUREMENT JR_CIRC`.trim());
      return { data: [], success: false };
    }
  },

  async approvalReprovalDocument(
    params: IProcurementApprovalReprovalParams,
    processType: string
  ): Promise<ApprovalResponse<IProcurement[]>> {
    try {
      const { data, status } = await api.put<IProcurement[]>(
        processType.includes("JR")
          ? routes.approvalReprovalJRDocument(processType)
          : routes.approvalReprovalExtMarketDocument(
              processType,
              params.documentNumber || "0"
            ),
        params
      );

      if (status === 200) {
        const { updatedHeaderData, updatedDetailData } =
          await this.updateDataAfterApproval(processType);

        return {
          data: {
            header: updatedHeaderData,
            detail: updatedDetailData,
          },
          message: `${
            params.operation === 1
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.documentNumber}`,
          success: status === 200,
        };
      }

      return {
        data: { header: [], detail: [] },
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${
          params.documentNumber
        }`,
      };
    } catch (error) {
      handleApiError(error, t, `PROCUREMENT ${processType || ""}`.trim());
      return {
        data: { header: [], detail: [] },
        success: false,
      };
    }
  },

  filterDocumentDetails(
    detailData: IProcurement[],
    documentNumber: string
  ): IProcurement[] {
    if (!detailData?.length) return [];

    return detailData.filter(
      (detail: IProcurement) =>
        detail.numeroSolicitacao?.toString().trim() ===
        documentNumber?.toString().trim()
    );
  },

  async updateDataAfterApproval(process: string): Promise<{
    updatedHeaderData: IProcurement[];
    updatedDetailData: IProcurement[];
  }> {
    try {
      const { data } = await this.getPendingApprovals(process);

      if (!data) {
        return {
          updatedHeaderData: [],
          updatedDetailData: [],
        };
      }

      return {
        updatedHeaderData: data.header,
        updatedDetailData: [],
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },
} as const;
