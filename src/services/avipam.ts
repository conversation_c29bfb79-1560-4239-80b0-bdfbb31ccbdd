import { APIAvipam } from "@/api";
import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import i18n from "@/hooks/translation";
import { IAvipam } from "@/interfaces";
import { AvipamItem } from "@/interfaces/avipam";
import { t } from "i18next";
import { toast } from "react-toastify";

export const AvipamService = {
  /**
   * Prepara os dados para a tabela principal
   */
  prepareTableData: (headerData: AvipamItem[]): AvipamItem[] => {
    if (!headerData.length) return [];

    // Filter out items where summary or total is null
    return headerData.filter(({ summary, total }) => {
      return summary !== null && total !== null;
    });
  },
  /**
   * Busca e prepara as aprovações pendentes
   */
  fetchAndPreparePendingApprovals: async (
    cache: boolean = true
  ): Promise<IApprovalResponse<AvipamItem>> => {
    try {
      const { data, success } = await APIAvipam.getPendingApprovals(cache);

      if (!data || !data.length) {
        return { data: { header: [], detail: [] }, success, message: "" };
      }

      const preparedData = AvipamService.prepareTableData(data).map((item) => {
        return {
          ...item,
          origin: "AVIPAM",
          type: "VI",
        };
      });

      return {
        data: { header: preparedData, detail: [] },
        success,
        message: "",
      };
    } catch (error) {
      console.error("Error fetching pending approvals:", error);
      toast.error(i18n.t("common.errorFetchingData"));
      throw error;
    }
  },

  /**
   * Atualiza os dados após aprovação/reprovação
   */
  updateDataAfterApproval: async (
    currentHeaderData: AvipamItem[]
  ): Promise<{
    updatedHeaderData: AvipamItem[];
  }> => {
    try {
      const { data } = await AvipamService.fetchAndPreparePendingApprovals();

      if (!data || !Array.isArray(data) || data.length === 0) {
        // Return current data if no response
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      return { updatedHeaderData: data };
    } catch (error) {
      console.error("Erro ao atualizar dados após aprovação:", error);
      toast.error("Erro ao atualizar dados após aprovação");
      throw error;
    }
  },

  /**
   * Executa a aprovação/reprovação de um documento
   */
  approvalReprovalDocument: async ({
    orderNumber,
    status,
    reason,
  }: IAvipam.ApprovalReprovalParams): Promise<
    ApprovalResponse<AvipamItem[]>
  > => {
    try {
      const { data, success } = await APIAvipam.approvalReprovalDocument({
        orderNumber,
        status,
        reason,
      });

      if (success) {
        const { updatedHeaderData } =
          await AvipamService.updateDataAfterApproval(data);

        return {
          success: true,
          message: `${
            status === "A" ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${orderNumber}`,
          data: {
            header: updatedHeaderData,
          },
        };
      }

      return {
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${orderNumber}`,
        data: {
          header: [],
        },
      };
    } catch (error) {
      console.error("Error in Avipam approval/reproval:", error);
      throw error;
    }
  },
} as const;
