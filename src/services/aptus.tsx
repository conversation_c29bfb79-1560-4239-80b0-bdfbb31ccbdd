import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import {
  FileDownloadOutlined,
  NotificationsOutlined,
  WarningAmberOutlined,
} from "@mui/icons-material";
import { t } from "i18next";
import { api } from "../api/api";
import * as routes from "../api/routes/aptus";
import { Intdcenter } from "../constant/processesProps/styles";
import { IAptus } from "../interfaces";
import {
  IItens,
  ISubTotalPorDespesas,
  ISubTotalPorDias,
} from "../interfaces/IAptus";
import { IResponse } from "../interfaces/IResponse";
import { getLocalStorageItem } from "../utils/storage";
import { getUniqueDocs } from "../utils/UniqueDocs";
import { handleApiError } from "./error";

export type AptusProcessType =
  | "PC_APTUS"
  | "CC_APTUS"
  | "DR_APTUS"
  | "PD_APTUS"
  | "PC_APTUS"
  | "IE_APTUS"
  | "AD_TK"
  | "PC_TK"
  | "PD_TK"
  | undefined;

export const AptusService = {
  /**
   * Prepara os dados para a tabela principal
   */
  async prepareTableData(
    headerData: IAptus.IAptusBaseDocument[]
  ): Promise<IAptus.IAptusBaseDocument[]> {
    return getUniqueDocs(headerData).map((doc) => {
      const foundItem = headerData?.find(
        (resp: IAptus.IAptusBaseDocument) => resp.Codigo === doc
      );
      return foundItem || ({} as IAptus.IAptusBaseDocument);
    });
  },
  async fetchAndPreparePendingApprovals(
    processType: AptusProcessType,
    cache: boolean = true
  ): Promise<IApprovalResponse<IAptus.IAptusBaseDocument>> {
    try {
      const { data, status } = await api.get(
        routes.getPendingApprovals(processType),
        {
          params: { cache },
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );
      const preparedData = (await this.prepareTableData(data.Dados)).map(
        (item) => {
          return {
            ...item,
            origin: "APTUS",
            type: processType,
          };
        }
      );

      return {
        data: { header: preparedData, detail: [] },
        message: "",
        success: status === 200,
      };
    } catch (error: unknown) {
      handleApiError(error, t, `APTUS ${processType || ""}`.trim());
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },

  async approvalReprovalDocument({
    codigo,
    isApprove,
    processType,
    comentary,
  }: IAptus.ApprovalReprovalParams): Promise<
    ApprovalResponse<IAptus.IAptusBaseDocument[]>
  > {
    try {
      const baseUrl = isApprove
        ? routes.approve(processType ?? "", codigo)
        : routes.reproval(processType ?? "", codigo, comentary ?? "");

      const { data, status } = await api.put(baseUrl, {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      });

      if (status === 200) {
        const { updatedHeaderData } = await this.updateDataAfterApproval(
          processType,
          []
        );

        return {
          data: {
            header: updatedHeaderData,
          },
          success: true,
          message: `${
            isApprove ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${codigo}`,
        };
      }

      return {
        data,
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${codigo}`,
      };
    } catch (error: unknown) {
      handleApiError(error, t, `APTUS ${processType || ""}`.trim());
      return {
        data: { header: [] as IAptus.IAptusBaseDocument[] },
        success: false,
      };
    }
  },

  async getDetails(
    Codigo: string,
    processType?: AptusProcessType
  ): Promise<IResponse<IAptus.IAptusBaseDocument>> {
    try {
      const { data, status } = await api.get(
        `${routes.getDetails(processType)}/${Codigo}`
      );
      return { data, success: status === 200 };
    } catch (error: unknown) {
      handleApiError(error, t, `APTUS ${processType || ""}`.trim());
      return { data: {} as IAptus.IAptusBaseDocument, success: false };
    }
  },
  async updateDataAfterApproval(
    process: AptusProcessType,
    currentHeaderData: IAptus.IAptusBaseDocument[]
  ): Promise<{ updatedHeaderData: IAptus.IAptusBaseDocument[] }> {
    try {
      // Execute the approval process
      const { data } = await this.fetchAndPreparePendingApprovals(process);

      if (!data || !Array.isArray(data) || data.length === 0) {
        // Return current data if no response
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      // Prepare the table data with the updated header data

      return {
        updatedHeaderData: data,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      // Return current data in case of error to maintain state
      return {
        updatedHeaderData: currentHeaderData,
      };
    }
  },
  handleArrayDespesasDia(row: IAptus.IAptusBaseDocument) {
    const subTotalPorDias = row.SubTotalPorDias;
    const newItem: ISubTotalPorDias[] = subTotalPorDias.map(
      (dias: ISubTotalPorDias) => {
        const subDespesas: ISubTotalPorDespesas[] = [];
        row.SubTotalPorDespesas.forEach((despesa: ISubTotalPorDespesas) => {
          if (dias.Data === despesa.Data) {
            subDespesas.push(despesa);
            dias.SubTotalPorDespesas = subDespesas;
          }
        });
        return dias;
      }
    );

    newItem.forEach((item: ISubTotalPorDias) => {
      item.SubTotalPorDespesas.forEach((despesa: ISubTotalPorDespesas) => {
        const itens: IItens[] = [];
        row.Itens.forEach((i: IItens) => {
          if (
            despesa.Data === i.DataDespesa &&
            despesa.DespesaDescricao === i.ContaContabilDescricao
          ) {
            itens.push(i);
            despesa.Itens = itens;
          }
        });

        return despesa;
      });
    });

    return newItem;
  },
  async itemDropdown(idDivCollapse: string, ref: any) {
    if (!ref || !ref.current) return;
    const divEl = document.querySelector<HTMLDivElement>(
      `#collapse-${idDivCollapse}`
    );
    const bodyTable = document.querySelector<HTMLBodyElement>(
      `#bodyItem-${idDivCollapse}`
    );

    const iconExpand = document.querySelector(`#iconExpand-${idDivCollapse}`);

    if (divEl) {
      divEl.classList.toggle("show");
      bodyTable?.classList.toggle("withBackground");
      iconExpand?.classList.toggle("rotateIcon");
    }
  },
  handleParticipantsColumn(
    quantidadeParticipantes: number,
    participantes: string,
    translation: any
  ) {
    if (!quantidadeParticipantes) quantidadeParticipantes = 0;

    if (quantidadeParticipantes === 0) {
      return (
        <Intdcenter colSpan={1}>{quantidadeParticipantes ?? "0"}</Intdcenter>
      );
    }
    if (quantidadeParticipantes > 0) {
      return (
        <Intdcenter colSpan={1}>
          <div data-tooltip={participantes}>
            {quantidadeParticipantes ?? "0"}
          </div>
        </Intdcenter>
      );
    }
    return "";
  },
  handleLighthouseColumn(item: IItens[]) {
    const containLighthouse = item.some((i: IItens) => {
      return i.Critica === "SIM";
    });
    return containLighthouse ? (
      <NotificationsOutlined fontSize="medium" color="error" />
    ) : (
      "-"
    );
  },
  handleAnomalyColumn(item?: IItens[]) {
    const containAnomaly = item?.some((i: IItens) => {
      return i.Anomalia;
    });
    return containAnomaly ? <WarningAmberOutlined color="error" /> : "-";
  },
  handleDownloadAttachment(file: string) {
    if (!file) {
      return "-";
    }
    return (
      <a href={file} download>
        <FileDownloadOutlined fontSize="medium" />
      </a>
    );
  },
  handleCAccountancy(item: IItens[]) {
    const cAcountancy = item?.map((i: IItens) => {
      return i.ContaContabil;
    });

    return cAcountancy[0];
  },
  handleOrder(item: IItens[]) {
    const order = item?.map((i: IItens) => {
      return i.OrdemInterna;
    });
    return order[0];
  },

  handleCC(item: IItens[]) {
    const CC = item?.map((i: IItens) => {
      return i.CentroCusto;
    });
    return CC[0];
  },
};
