import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { IOnelog } from "@/interfaces";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import * as APIOnelog from "../api/onelog";
import { ItemDetailProps } from "../interfaces/onelog";
import { handleApiError } from "./error";

export const OneLogService = {
  async fetchAndPreparePendingApprovals(
    cache: boolean = true
  ): Promise<IApprovalResponse<IOnelog.ClientProps>> {
    try {
      const { data, success } = await APIOnelog.getPendingApprovals(cache);

      const preparedData = data.map((item) => {
        return {
          ...item,
          aprovacoes: item.aprovacoes.map((aprovacao) => ({
            ...aprovacao,
            origin: "ONELOG",
            type: "ONE",
          })),
        };
      });

      return {
        data: { header: preparedData, detail: preparedData },
        message: "",
        success,
      };
    } catch (error: unknown) {
      handleApiError(error, t, "ONELOG", "ONE");
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },
  async getDocumentDetails(documentNumber: string): Promise<ItemDetailProps> {
    try {
      const { data } = await APIOnelog.getDocumentDetails(documentNumber);
      return data;
    } catch (error) {
      console.error("Erro ao buscar detalhes do documento:", error);
      throw error;
    }
  },

  filterDocumentDetails(
    detailData: IOnelog.ClientProps[],
    rowNome: string
  ): IOnelog.AprovacoesProps[] {
    if (!detailData?.length) return [];

    return detailData
      .filter((detail: IOnelog.ClientProps) => detail.nome === rowNome)
      .flatMap((detail: IOnelog.ClientProps) => detail.aprovacoes || []);
  },

  async updateDataAfterApproval(): Promise<{
    updatedData: IOnelog.ClientProps[];
  }> {
    try {
      const { data } = await this.fetchAndPreparePendingApprovals(false);

      if (!data) {
        return {
          updatedData: [],
        };
      }

      return {
        updatedData: data.header,
      };
    } catch (error) {
      console.error("Erro ao atualizar dados após aprovação:", error);
      throw error;
    }
  },

  approvalReprovalDocument: async ({
    status,
    id,
    reason,
  }: IOnelog.ApprovalReprovalParams): Promise<
    IApprovalResponse<IOnelog.ClientProps>
  > => {
    try {
      const { data, success } = await APIOnelog.approvalReprovalDocument(
        status,
        id,
        getLocalStorageItem("employeeid") ?? "",
        reason
      );

      if (data && success) {
        const { updatedData } = await OneLogService.updateDataAfterApproval();

        return {
          success,
          data: { header: updatedData, detail: updatedData },
          message: `${
            status === "A" ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${id}`,
        };
      }

      return {
        success: false,
        data: { header: [], detail: [] },
        message: `${t("Error.in.approval")} ${t("Document")} ${id}`,
      };
    } catch (error) {
      console.error("Error in OneLog approval/reproval:", error);
      throw error;
    }
  },

  approvalReprovalDetailDocument: async ({
    status,
    id,
    motivo,
    submotivo,
    reason,
    employeeID,
  }: IOnelog.ApprovalReprovalParams & { employeeID: string }): Promise<
    ApprovalResponse<IOnelog.ClientProps[]>
  > => {
    try {
      const { data, success } = await APIOnelog.approvalReprovalDetailDocument(
        status,
        id,
        motivo ?? "",
        submotivo ?? "",
        employeeID,
        reason
      );

      if (data && success) {
        const { updatedData } = await OneLogService.updateDataAfterApproval();

        return {
          success,
          data: { header: updatedData, detail: updatedData },
          message: `${
            status === "A" ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${id}`,
        };
      }

      return {
        success: false,
        data: { header: [], detail: [] },
        message: `${t("Error.in.approval")} ${t("Document")} ${id}`,
      };
    } catch (error) {
      console.error("Error in OneLog approval/reproval:", error);
      throw error;
    }
  },
} as const;
