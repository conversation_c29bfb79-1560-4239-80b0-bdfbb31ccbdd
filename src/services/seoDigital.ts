import { ApprovalResponse } from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { t } from "i18next";
import { toast } from "react-toastify";
import { api } from "../api/api";
import * as routes from "../api/routes/seoDigital";
import i18n from "../hooks/translation";
import { ISeoDigital } from "../interfaces";
import { IResponse } from "../interfaces/IResponse";
import { getLocalStorageItem } from "../utils/storage";
import { handleApiError } from "./error";

export const SeoDigitalService = {
  async getPendingApprovals(
    cache: boolean = true
  ): Promise<IApprovalResponse<ISeoDigital.ISeoDigitalBase>> {
    try {
      const { data, status } = await api.get(routes.getPendingApprovals, {
        params: { cache },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      });
      return {
        data: { header: data.retorno, detail: [] },
        success: status === 200,
        message: "",
      };
    } catch (error: unknown) {
      handleApiError(error, t, "Seo Digital");
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },

  async approvalReprovalDocument({
    processCode,
    typeOfApproval,
    status,
    reason,
  }: ISeoDigital.ApprovalReprovalParams): Promise<
    ApprovalResponse<ISeoDigital.ISeoDigitalBase[]>
  > {
    try {
      const { data, status: respStatus } =
        status === "A"
          ? await api.post(routes.approve(typeOfApproval, processCode))
          : await api.post(
              routes.reproval(typeOfApproval, processCode, reason)
            );

      if (data && respStatus === 200) {
        const { updatedHeaderData } = await this.updateDataAfterApproval();
        return {
          data: { header: updatedHeaderData },
          success: true,
          message: `${
            status === "A" ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${processCode}`,
        };
      }

      return {
        data: { header: [] as ISeoDigital.ISeoDigitalBase[] },
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${processCode}`,
      };
    } catch (error: unknown) {
      handleApiError(error, t, "Seo Digital");
      return { data: { header: [] }, success: false };
    }
  },

  async getDocumentDetails(
    documentId: string
  ): Promise<IResponse<ISeoDigital.ISeoDigitalBase>> {
    try {
      const { data, status } = await api.get(
        `${routes.getDetails()}/${documentId}?language=${i18n.language}`
      );
      return { data, success: status === 200 };
    } catch (error: unknown) {
      handleApiError(error, t, "Seo Digital");
      return { data: {} as ISeoDigital.ISeoDigitalBase, success: false };
    }
  },

  async updateDataAfterApproval(): Promise<{
    updatedHeaderData: ISeoDigital.ISeoDigitalBase[];
  }> {
    try {
      const { data } = await this.getPendingApprovals(false);

      if (!data || !Array.isArray(data) || data.length === 0) {
        return {
          updatedHeaderData: [],
        };
      }

      return { updatedHeaderData: data };
    } catch (error: unknown) {
      toast.error("Erro ao atualizar dados após aprovação");
      return { updatedHeaderData: [] };
    }
  },
};
