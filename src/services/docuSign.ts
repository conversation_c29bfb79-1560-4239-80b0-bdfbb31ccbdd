import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { IDocusign } from "@/interfaces";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import { api } from "../api/api";
import * as routes from "../api/routes/docuSign";
import { IResponse } from "../interfaces/IResponse";
import { handleApiError } from "./error";

export const DocuSignService = {
  async getPendingApprovals(
    cache: boolean = true
  ): Promise<IApprovalResponse<IDocusign.ItemProps>> {
    try {
      const { data, status } = await api.post(
        routes.getPendingApprovals(),
        {
          ENTRADA: {
            EVENTO: "GETDATA",
          },
        },
        {
          params: { cache },
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
            client_id: import.meta.env.VITE_DOCUSIGN_CLIENT_ID,
          },
        }
      );
      return {
        data: { header: data || [], detail: [] },
        success: status === 200,
        message: "",
      };
    } catch (error) {
      handleApiError(error, t, "DocuSign");
      return { data: { header: [], detail: [] }, success: false, message: "" };
    }
  },

  async approvalReprovalDocument(
    params: IDocusign.ItemProps & { status: "A" | "R" }
  ): Promise<IResponse<unknown>> {
    try {
      if (params.status === "A") {
        const { data, status } = await api.post(routes.approve(), params);
        return { data, success: status === 200 };
      } else {
        const { data, status } = await api.post(routes.reproval(), params);
        return { data, success: status === 200 };
      }
    } catch (error) {
      handleApiError(error, t, "DocuSign");
      return { data: { total: 0, tasks: [] }, success: false };
    }
  },

  async updateDataAfterApproval(
    row: IDocusign.ItemProps,
    headerData: IDocusign.ItemProps[],
    execute: () => void
  ): Promise<{ updatedHeaderData: IDocusign.ItemProps[] }> {
    const updatedData = headerData.map((item) =>
      item.DOCUMENTO === row.DOCUMENTO ? { ...item, status: "APPROVED" } : item
    );
    execute();
    return { updatedHeaderData: updatedData };
  },

  async getDocumentDetails(
    envelopeId: string,
    recipientId: string
  ): Promise<IResponse<IDocusign.ItemProps[]>> {
    try {
      const { data, status } = await api.post(
        routes.getPendingApprovals(),
        {
          ENTRADA: {
            EVENTO: "GETDETAIL",
            ADICIONAL1: envelopeId,
            ADICIONAL2: recipientId,
            TERMINAL: window.location.href,
          },
        },
        {
          headers: {
            client_id: import.meta.env.VITE_DOCUSIGN_CLIENT_ID,
          },
        }
      );
      return { data, success: status === 200 };
    } catch (error) {
      const a = [
        {
          DOCUMENTO: null,
          ITEM: null,
          FORNECEDOR: null,
          MATERIAL: null,
          QTD: null,
          VLUNIT: null,
          VLITEM: null,
          VLDOC: null,
          CENTRO: null,
          REQUISITANTE: null,
          TIPO: null,
          DATA_EMISSAO: null,
          ADICIONAIS: [
            {
              CAMPO: "ENVELOPEID",
              VALOR: "d2d19e07-ed97-4155-8ae2-ec35511bfae8",
            },
            {
              CAMPO: "RECIPIENTID",
              VALOR: "b8a3ae3a-c9e9-4f1d-ad81-9ec29b01fe3c",
            },
            {
              CAMPO: "EMAIL",
              VALOR: "<EMAIL>",
            },
            {
              CAMPO: "NOME",
              VALOR: "Michel Longhi",
            },
            {
              CAMPO: "CLIENTUSERID",
              VALOR: "<EMAIL>",
            },
            {
              CAMPO: "ROUTINGORDER",
              VALOR: "3",
            },
            {
              CAMPO: "ROLENAME",
              VALOR: "",
            },
            {
              CAMPO: "CONTA",
              VALOR: "c55d8c18-8c56-42c2-b402-1ea5e02dc184",
            },
            {
              CAMPO: "SUBJECT",
              VALOR: "CRW3638113 - CRW3638113.pdf",
            },
            {
              CAMPO: "SENTDATE",
              VALOR: "********",
            },
            {
              CAMPO: "SENTTIME",
              VALOR: "165902",
            },
            {
              CAMPO: "SENDER_USERNAME",
              VALOR: "Contratos BRF",
            },
            {
              CAMPO: "SENDER_EMAIL",
              VALOR: "<EMAIL>",
            },
            {
              CAMPO: "URL_1",
              VALOR:
                "https://na2.docusign.net/Signing/MTRedeem/v1/70f472d9-a815-4f99-aa5d-60a329640d80/na?slt=eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQYAAAABAAMABwCA5RyWbNDdSAgAgIUuHY7Q3UgYAAEAAAAAAAAAIQDqAgAAeyJUb2tlbklkIjoiYzMyZjkwZDAtOGYyMy00MzZhLWFlNTctN2Q1MDgxZGRkZjAxIiwiRXhwaXJhdGlvbiI6IjIwMjUtMDctMzFUMjA6MDM6MTUrMDA6MDAiLCJJc3N1ZWRBdCI6IjIwMjUtMDctMzFUMTk6NTg6MTUuMDc5MDQ4KzAwOjAwIiwiUmVzb3VyY2VJZCI6ImQyZDE5ZTA3LWVkOTctNDE1NS04YWUyLWVjMzU1MTFiZmFlOCIsIlJlc291cmNlcyI6IntcIkVudmVsb3BlSWRcIjpcImQyZDE5ZTA3LWVkOTctNDE1NS04YWUyLWVjMzU1MTFiZmFlOFwiLFwiQWN0b3JVc2VySWRcIjpcIjg4MDQxNWVmLTkwNTQtNDUzOC1hNjVmLWE1Yjc4Y2YzY2QwZlwiLFwiUmVjaXBpZW50SWRcIjpcImI4YTNhZTNhLWM5ZTktNGYxZC1hZDgxLTllYzI5YjAxZmUzY1wiLFwiRmFrZVF1ZXJ5U3RyaW5nXCI6XCJ0PTE2ZWU2MDA3LTlhMzMtNDczNi04M2IxLWVmZTM0ZThjNzAzMFwiLFwiSW50ZWdyYXRvcktleVwiOlwiYTU1MWI0NmYtYWM2Ni00NDNhLWFmODktNGQwZGU4Yzc5NzUyXCIsXCJDcmVhdGVkQXRcIjpcIjIwMjUtMDctMzFUMTk6NTg6MTUuMDUzODMyNVpcIn0iLCJUb2tlblR5cGUiOjEsIkF1ZGllbmN",
            },
            {
              CAMPO: "URL_2",
              VALOR:
                "lIjoiMjVlMDkzOTgtMDM0NC00OTBjLThlNTMtM2FiMmNhNTYyN2JmIiwiUmVkaXJlY3RVcmkiOiJodHRwczovL25hMi5kb2N1c2lnbi5uZXQvU2lnbmluZy9TdGFydEluU2Vzc2lvbi5hc3B4IiwiSGFzaEFsZ29yaXRobSI6MCwiSGFzaFJvdW5kcyI6MCwiVG9rZW5TdGF0dXMiOjAsIklzU2luZ2xlVXNlIjpmYWxzZX0_AACtVEht0N1I.Er3y7WXmaoD472hb1ocSeYKoikqd2IStjRjOHdnBR35F4i7VQvavRnAii7IioubiP9Vtk2Q7KB5csFoDoRExbyQbjvS6C14_slMwmMEx6Suxn8xW_e0uJD35I_rz1_mu4TGIt3b56LV8ug_5Co9U-UdBnW5EAiQxgvPObGCHp4U_KQ_XbobeigBo51CasEOIn23dGvOE_ZmxN0UHMx1YC4_OzvJ-_EKbyjp_WRY3s__9eN1Xys_Eiky5cUjz7qCsLFMlRLTQ1DzzZQUomi31cBn0KfHlH3x-JF3QP19nuB0SmF1yebh5wkv2oaIDbSiC8vBqyAoT1VrqVkI2iBhDDw",
            },
          ],
          PROCESSO: "DS",
          SIGNERS: undefined,
        },
      ];
      handleApiError(error, t, "DocuSign");
      return { data: a as IDocusign.ItemProps[], success: false };
    }
  },
};
