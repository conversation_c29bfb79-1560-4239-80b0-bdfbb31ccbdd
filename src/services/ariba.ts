import { APIAriba } from "@/api";
import {
  ApprovalOrigin,
  ApprovalResponse,
} from "@/api/approval/approval.service";
import { IApprovalResponse } from "@/api/approval/strategy/base.service";
import { IAriba } from "@/interfaces";
import { t } from "i18next";

export const AribaService = {
  fetchAndPreparePendingApprovals: async (
    cache: boolean = true
  ): Promise<
    IApprovalResponse<IAriba.AribaItemType | IAriba.PendingApprovalsProps>
  > => {
    try {
      const { data, success } = await APIAriba.getPendingApprovals(cache);

      const preparedData = AribaService.prepareTableData(data);

      return {
        data: {
          header: [preparedData],
          detail: [],
        },
        message: "",
        success,
      };
    } catch (error) {
      console.error("Error fetching pending approvals:", error);
      return {
        data: {
          header: [],
          detail: [],
        },
        message: t("Error.fetching.approvals"),
        success: false,
      };
    }
  },
  /**
   * Prepara os dados para a tabela principal
   * Retorna um objeto onde cada chave é o processName e o valor é um array de itens
   */
  prepareTableData: (
    data: IAriba.PendingApprovalsProps
  ): Record<string, IAriba.AribaItemType[]> => {
    if (!data) return {};

    const processes = {
      sourcing: "AS",
      project: "AP",
      contract: "AT",
      requisition: "AR",
    };

    const headerData = Object.entries(data)
      .filter(
        ([key, value]) =>
          key !== "others" &&
          typeof value === "object" &&
          value &&
          Array.isArray(value.tasks)
      )
      .flatMap(([key, value]) => {
        return value.tasks.map(
          (task: {
            detail: IAriba.AribaItemType;
            description: string;
          }): IAriba.AribaItemType => ({
            ...task.detail,
            description: task.description,
            type: processes[key as keyof typeof processes],
            processName: key,
            origin: "ARIBA" as ApprovalOrigin,
          })
        );
      });

    // Agrupa os itens por processName, cada chave é o processName e o valor é um array de itens
    const groupedByType = headerData.reduce((acc, item) => {
      const type = item.processName || "others";
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(item);
      return acc;
    }, {} as Record<string, IAriba.AribaItemType[]>);

    return groupedByType;
  },

  /**
   * Ordena as requisições por ordem de prioridade
   */
  sortApprovalRequests(
    requests: IAriba.ApprovalRequest[]
  ): IAriba.ApprovalRequest[] {
    if (!requests || !Array.isArray(requests)) {
      return [];
    }
    const map = new Map(requests.map((request) => [request.id, request]));
    const sortedRequests: IAriba.ApprovalRequest[] = [];
    const visited = new Set<string>();

    const visit = (request: IAriba.ApprovalRequest) => {
      if (!visited.has(request.id)) {
        visited.add(request.id);
        request.dependencyApprovalRequestsUID.forEach((dependencyId) => {
          const dependency = map.get(dependencyId);
          if (dependency) visit(dependency);
        });
        sortedRequests.push(request);
      }
    };

    requests.forEach(visit);

    return sortedRequests;
  },

  /**
   * Filtra os detalhes de um documento específico
   */
  filterDocumentDetails: (
    detailData: IAriba.AribaItemType[],
    documentNumber: string
  ): IAriba.AribaItemType[] => {
    if (!detailData?.length) return [];

    return detailData.filter(
      (detail: IAriba.AribaItemType) =>
        detail.task?.toString().trim() === documentNumber?.toString().trim()
    );
  },

  /**
   * Atualiza os dados após aprovação/reprovação
   */
  updateDataAfterApproval: async ({
    process,
    currentHeaderData,
  }: {
    process: string;
    currentHeaderData: IAriba.AribaItemType[];
  }): Promise<{
    updatedHeaderData: IAriba.AribaItemType[];
  }> => {
    try {
      const { data } = (await APIAriba.getPendingApprovals(false)) as {
        data: IAriba.PendingApprovalsProps;
      };

      if (!data) {
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      const aribaProcessMapping = {
        AS: "sourcing",
        AP: "project",
        AT: "contract",
        AR: "requisition",
      } as const;

      type AribaProcessType = keyof typeof aribaProcessMapping;
      const dataSource = aribaProcessMapping[process as AribaProcessType];

      const tasksData =
        data[dataSource]?.tasks.map((task: any) => ({
          ...task.detail,
          description: task.description,
          type: process,
          origin: "ARIBA" as ApprovalOrigin,
          aribaProcessType: process, // Garantindo que o processo também seja preservado
        })) || [];

      return {
        updatedHeaderData: tasksData,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },
  /**
   * Executa a aprovação/reprovação de um documento
   */
  approvalReprovalDocument: async (params: {
    itemCode: string;
    action: string;
    comment: string;
    aribaUser: string;
    typeProcess: string;
  }): Promise<ApprovalResponse<IAriba.AribaItemType[]>> => {
    try {
      const { data, success } = await APIAriba.approvalReprovalDocument(params);

      if (success) {
        const { updatedHeaderData } =
          await AribaService.updateDataAfterApproval({
            process: params.typeProcess,
            currentHeaderData: [],
          });

        return {
          message: `${
            params.action === "A"
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.itemCode}`,
          data: {
            header: updatedHeaderData,
          },
          success: true,
        };
      }

      return {
        success,
        data: {
          header: data,
        },
        message: `${t("Error.in.approval")} ${t("Document")} ${
          params.itemCode
        }`,
      };
    } catch (error) {
      console.error("Error in Ariba approval/reproval:", error);
      throw error;
    }
  },
} as const;
