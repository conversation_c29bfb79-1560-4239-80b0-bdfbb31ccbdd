import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { AllProcessesTypes } from "../components/AccordionTable";
import { processesProps } from "../constant";
import { IAccordionTableProps, IAriba, IUser } from "../interfaces";

export type ExtraParamsType = {
  language: string;
  cacheLiveTime: number;
  ecp: string;
  cacheActive: string;
  ano?: number;
  aprovador?: string;
  timeKeepCache?: string;
  [key: string]: any;
};

const activeMock = false && !!import.meta.env.DEV;
export const aribaPermissions = ["AT", "AP", "AR", "AS"];

interface CardsState {
  // Estado
  cards: IAccordionTableProps<any>[];
  reloadWithoutCache: boolean;
  // Ações
  setCards: (cards: IAccordionTableProps<any>[]) => void;
  setReloadWithoutCache: (reload: boolean) => void;

  updateDataAndCounter: (
    processId: string,
    newTotal: number,
    headerData: AllProcessesTypes[],
    detailData?: AllProcessesTypes[]
  ) => void;

  handleAribaResponse: (aribaData: {
    header: IAriba.PendingApprovalsProps[];
    detail: IAriba.PendingApprovalsProps[];
  }) => void;
  handleCardResponse: <T>(header: T[], detail: T[]) => Promise<void>;
}

export const useCardsStore = create<CardsState>()(
  subscribeWithSelector((set, get) => ({
    // Estado inicial
    cards: [] as IAccordionTableProps<any>[],
    reloadWithoutCache: false,

    // Ações básicas
    setCards: (cards) => set({ cards }),
    setReloadWithoutCache: (reloadWithoutCache) => set({ reloadWithoutCache }),

    // Função para atualizar o total de um card específico
    updateDataAndCounter(
      processId: string,
      newTotal: number,
      headerData: AllProcessesTypes[],
      detailData?: AllProcessesTypes[]
    ) {
      const { cards } = get();

      if (newTotal === 0) {
        // Remove cards with zero items
        const filteredCards = cards.filter(
          (card) => !(card.process === processId || card.type === processId)
        );
        set({ cards: filteredCards });
      } else {
        // Update counter for non-zero items
        const updatedCards = cards.map((card) => {
          // Identifica o card pelo process ou type (para caso especial AD)
          if (card.process === processId || card.type === processId) {
            return {
              ...card,
              headerData,
              detailData: detailData || [],
              total: newTotal,
            };
          }
          return card;
        });
        set({ cards: updatedCards });
      }
    },

    // Handler para resposta Ariba
    handleAribaResponse: (aribaData: {
      header: IAriba.PendingApprovalsProps[];
      detail: IAriba.PendingApprovalsProps[];
    }) => {
      const [header] = aribaData.header;

      if (!header) return;

      const processType = {
        sourcing: "AS",
        project: "AP",
        contract: "AT",
        requisition: "AR",
      };

      const processTypes = Object.keys(header);

      processTypes.forEach((dataSource) => {
        const permissionAribaHeaderData =
          (header[dataSource as keyof typeof header] as IAriba.TaskGroup) || {};

        if (permissionAribaHeaderData.tasks.length > 0) {
          const process = processType[dataSource as keyof typeof processType];
          const specificProcessConfig = processesProps(process)[0];
          const { cards, setCards } = get();

          if (!cards.some((card) => card.process === process)) {
            const aribaCard: IAccordionTableProps<any> = {
              headerData: permissionAribaHeaderData.tasks,
              process: specificProcessConfig?.type || "",
              title: specificProcessConfig?.title || "",
              type: specificProcessConfig?.type || "",
              total: permissionAribaHeaderData.tasks.length,
              origin: specificProcessConfig?.origin || "",
            };

            const newCards = [...cards, aribaCard].sort((a, b) =>
              a.title.localeCompare(b.title)
            );

            setCards(newCards);
          }
        }
      });
    },
    // Handler para resposta de cards
    handleCardResponse: async <T>(header: T[], detail: T[]): Promise<void> => {
      if (!header || !detail) return;

      const { setCards, cards, handleAribaResponse } = get();

      // Get origin and type from document or use defaults
      const getDocumentKey = (doc: any) => {
        const origin = doc.origin as string;
        const type = doc.type as string;
        return { origin, type, key: `${origin}_${type}` };
      };

      // Agrupar header por origin e type
      const groupedHeader = header.reduce((groups, doc) => {
        const { origin, type, key } = getDocumentKey(doc);
        if (!groups[key]) {
          groups[key] = {
            header: [],
            detail: [],
            origin,
            type,
          };
        }
        groups[key].header.push(doc);
        return groups;
      }, {} as Record<string, { header: T[]; detail: T[]; origin: string; type: string }>);

      // Agrupar detail por origin e type
      const groupedDetail = detail.reduce((groups, doc) => {
        const { origin, type, key } = getDocumentKey(doc);
        if (!groups[key]) {
          groups[key] = {
            header: [],
            detail: [],
            origin,
            type,
          };
        }
        groups[key].detail.push(doc);
        return groups;
      }, {} as Record<string, { header: T[]; detail: T[]; origin: string; type: string }>);

      // Juntar os dois agrupamentos
      const allKeys = new Set([
        ...Object.keys(groupedHeader),
        ...Object.keys(groupedDetail),
      ]);

      const groupedDocuments: Record<
        string,
        { header: T[]; detail: T[]; origin: string; type: string }
      > = {};

      allKeys.forEach((key) => {
        groupedDocuments[key] = {
          header: groupedHeader[key]?.header || [],
          detail: groupedDetail[key]?.detail || [],
          origin:
            groupedHeader[key]?.origin || groupedDetail[key]?.origin || "",
          type: groupedHeader[key]?.type || groupedDetail[key]?.type || "",
        };
      });

      // Criar cards para cada grupo
      const newCards: IAccordionTableProps<any>[] = [];

      for (const group of Object.values(groupedDocuments)) {
        const processConfig = processesProps(group.type)[0];
        if (!processConfig) continue;

        if (processConfig.origin === "ARIBA" && group.header.length > 0) {
          handleAribaResponse({
            header: group.header as unknown as IAriba.PendingApprovalsProps[],
            detail: group.detail as unknown as IAriba.PendingApprovalsProps[],
          });
          continue;
        }

        newCards.push({
          headerData: group.header,
          detailData: group.detail,
          process: processConfig.permission || "",
          title: processConfig.title || "",
          type: group.type,
          origin: group.origin,
          total: group.header.length,
        });
      }

      // Adicionar apenas cards que ainda não existem
      const uniqueNewCards = newCards.filter(
        (newCard) =>
          !cards.some(
            (existingCard) =>
              existingCard.type === newCard.type &&
              existingCard.origin === newCard.origin
          )
      );

      if (uniqueNewCards.length > 0) {
        const updatedCards = [...cards, ...uniqueNewCards].sort((a, b) =>
          a.title.localeCompare(b.title)
        );

        setCards(updatedCards);
      }
    },
  }))
);

export const useCards = (user?: IUser) => {
  const store = useCardsStore();

  return {
    cards: store.cards,
    setCards: store.setCards,
    handleCardResponse: store.handleCardResponse,
    reloadWithoutCache: store.reloadWithoutCache,
    setReloadWithoutCache: store.setReloadWithoutCache,
    updateDataAndCounter: store.updateDataAndCounter,
  };
};
