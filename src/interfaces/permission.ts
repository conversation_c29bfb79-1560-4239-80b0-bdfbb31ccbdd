interface BaseApproval {
  created?: string;
  updated?: string;
  process: string;
}

export interface IPermissionsApproval extends BaseApproval {
  accountId: number;
  active: boolean;
  additionalJson?: string;
  id: number;
  process: string;
  typeProcess?: string;
  origin?: string;
}

export interface IProcessApproval extends BaseApproval {
  document: string;
  initials: string;
  item: string;
  typeProcess: string;
}
